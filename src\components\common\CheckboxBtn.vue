<script setup lang="ts">
const props = defineProps<{
  label: string;
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

const check = () => {
  emit('update:modelValue', !props.modelValue);
};
</script>

<template>
  <q-btn class="check-btn" @click="check" no-caps rounded unelevated padding="12px 24px">
    <q-icon
      class="q-mr-sm"
      :name="props.modelValue ? 'check_box' : 'check_box_outline_blank'"
      :style="{ color: props.modelValue ? '#3D3C91' : '#9E9E9E' }"
    />
    {{ props.label }}
  </q-btn>
</template>

<style scoped>
.check-btn {
  /* Style=Checkbox */

  box-sizing: border-box;

  /* Auto layout */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  align-content: flex-start;
  padding: 12px 24px;
  gap: 10px;

  width: 144px;
  height: 48px;

  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;

  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}
</style>
