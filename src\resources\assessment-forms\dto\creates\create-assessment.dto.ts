import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AssessmentType } from '../../enums/assessment-type.enum';

export class CreateAssessmentDto {
  @ApiProperty({
    description: 'รหัสผู้สร้างแบบประเมิน',
    example: 102,
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  creatorUserId: number;

  @ApiProperty({
    description: 'ประเภทของแบบประเมิน (QUIZ หรือ FORM)',
    enum: AssessmentType,
    example: 'QUIZ',
    enumName: 'AssessmentType',
  })
  @IsNotEmpty()
  @IsEnum(AssessmentType, { message: 'Type must be either FORM or QUIZ' })
  type: AssessmentType;

  @ApiProperty({
    description: 'รหัสโปรแกรม',
    example: 202,
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  programId: number;
}
