import { <PERSON>NotEmpty, <PERSON>N<PERSON>ber, <PERSON>E<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ItemBlockType } from '../../enums/item-block-type.enum';

export class CreateBlockDto {
  @ApiProperty({
    description: 'ID of the assessment this block belongs to',
    type: Number,
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  assessmentId: number;

  @ApiProperty({
    description: 'The type of block to create',
    enum: ItemBlockType,
    enumName: 'ItemBlockType',
    example: ItemBlockType.RADIO,
  })
  @IsNotEmpty()
  @IsEnum(ItemBlockType)
  type: ItemBlockType;
}
