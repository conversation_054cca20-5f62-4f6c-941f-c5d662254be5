<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from 'src/stores/auth';
import utilsConfigs from 'src/configs/utilsConfigs';

import { QIcon, QInput, QBtn, QForm } from 'quasar';

const showpass = ref(false);
const authStore = useAuthStore();

const form = ref<QForm | null>(null);

const login = async () => {
  const valid = await form.value?.validate();
  if (valid) {
    await authStore.loginBuu();
  }
};
</script>

<template>
  <q-page class="row">
    <div
      class="col flex relative-position"
      :style="{ 'background-color': utilsConfigs.colorSystem }"
    >
      <q-icon name="tv" size="300px" color="white" style="opacity: 0.737; margin: auto"></q-icon>
      <div class="absolute text-white" style="inset: 0; left: 2em; top: 95%">
        Crafted by the computer center of Burapha University. © 2025 Copyright All Rights Reserved.
      </div>
    </div>

    <div class="col-auto q-my-auto">
      <div style="width: 600px" class="flex justify-center row">
        <q-icon
          name="swap_horiz"
          :style="{ color: utilsConfigs.colorSystem }"
          size="200px"
          class="q-mx-auto q-mb-md col-12"
        ></q-icon>
        <div style="width: 400px" class="col-12 fontSarabun">
          <q-form ref="form" @submit.prevent="login" class="q-gutter-y-lg">
            <q-input
              dense
              outlined
              data-cy="login_username"
              label="ชื่อผู้ใช้"
              v-model="authStore.loginUsername"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกชื่อผู้ใช้']"
              @keyup.enter="login"
            >
              <template v-slot:prepend>
                <q-icon name="account_circle" :color="utilsConfigs.colorSystem"></q-icon>
              </template>
            </q-input>
            <q-input
              :type="showpass ? 'text' : 'password'"
              dense
              outlined
              data-cy="login_password"
              label="รหัสผ่าน"
              v-model="authStore.loginPassword"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกรหัสผ่าน']"
              @keyup.enter="login"
            >
              <template v-slot:prepend>
                <q-icon name="lock" :color="utilsConfigs.colorSystem"></q-icon>
              </template>
              <template v-slot:append>
                <q-icon
                  :data-cy="showpass ? 'i-eye' : 'i-eyeOff'"
                  :name="showpass ? 'visibility' : 'visibility_off'"
                  :color="utilsConfigs.colorSystem"
                  @click="showpass = !showpass"
                  class="cursor-pointer"
                ></q-icon>
              </template>
            </q-input>
            <q-btn
              type="submit"
              unelevated
              dense
              class="text-white full-width"
              data-cy="login_btn"
              :style="{ 'background-color': utilsConfigs.colorSystem }"
              label="เข้าสู่ระบบ"
              style="font-size: 16px"
            >
            </q-btn>
          </q-form>
          <div class="q-mt-lg flex justify-center">
            <a href="https://myid.buu.ac.th/" class="text-accent q-mt-sm font-weight-regular"
              >ลืมรหัสผ่าน?</a
            >
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>
