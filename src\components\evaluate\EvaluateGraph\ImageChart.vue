<template>
  <div class="grid-container">
    <div v-for="(dataset, index) in props.data" :key="index" class="grid-item">
      <div>
        <a :href="dataset.label" target="_blank">
          <q-img :src="dataset.label" class="image"></q-img>
        </a>
        <div class="label">{{ props.labels[index] }}</div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
interface Dataset {
  label: string;
  values: number[];
}

const props = defineProps<{
  labels: string[];
  data: Dataset[];
}>();
console.log(props.labels);


</script>
<style>
.grid-item > div {
  overflow: hidden;
  border: 2px solid #474747; 
  border-radius: 21px;

}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 200px); /* 3 columns, each 200px wide */
  gap: 50px; /* Spacing between grid items */
  padding: 30px; /* Padding around the grid */
  justify-content: center; /* Center the grid */

}

.grid-item {
  display: flex;
  flex-direction: column; /* Stack image and label vertically */
  align-items: center; /* Center content horizontally */
}

.image {
  width: 200px; /* Fixed width */
  height: 150px; /* Fixed height */
  object-fit: cover; /* Crop image to fit square */

}

.label {
  margin-top: 10px; /* Space between image and label */
  font-size: 14px; /* Adjust font size as needed */
  text-align: center; /* Center the label text */
  color: #333; /* Text color */
  padding-bottom: 10px;
}
</style>
