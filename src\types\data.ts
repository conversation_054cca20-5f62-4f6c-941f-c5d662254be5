export type DataResponse<T> = {
  data: Array<T>;
  total: number;
  curPage: number;
  hasPrev: boolean;
  hasNext: boolean;
};

export type DataParams = {
  sortBy: string | undefined | null;
  order: 'ASC' | 'DESC';
  limit: number;
  page: number;
  search: string | undefined | null;
};

export type AssessmentType = 'quiz' | 'evaluate';

export type AssessmentQueryParams = DataParams & {
  type: AssessmentType;
};
