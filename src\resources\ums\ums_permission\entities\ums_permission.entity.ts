import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'ums_permission' })
export class UmsPermission {
  @PrimaryGeneratedColumn({ comment: 'รหัสสิทธิ์' })
  perId: number;

  @Column({ comment: 'ชื่อสิทธิ์ภาษาไทย', length: 50, nullable: false })
  perNameTh: string;

  @Column({ comment: 'ชื่อสิทธิ์ภาษาอังกฤษ', length: 50 })
  perNameEn: string;

  @Column({
    comment: 'สถานะ Y = เปิด N = ปิด',
    length: 1,
    default: 'Y',
  })
  perStatus: string;

  @Column({
    comment: 'สิทธ์ตั้งต้น Y = เปิด N = ปิด',
    length: 1,
    default: 'Y',
  })
  perDefault: string;

  @CreateDateColumn({ comment: 'วันที่สร้าง', nullable: true })
  createAt: Date;

  @DeleteDateColumn({ comment: 'วันที่ลบ', nullable: true })
  deleteAt: Date;

  @UpdateDateColumn({ comment: 'วันที่อัพเดท', nullable: true })
  updateAt: Date;

  @Column({ comment: 'คนที่อัพเดท', length: 100, nullable: true })
  updateUser: string;

  @Column({ comment: 'โปรแกรมที่อัพเดท', length: 100, nullable: true })
  updateProgram: string;
}
