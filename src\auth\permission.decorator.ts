// permission.decorator.ts
import { applyDecorators, UseGuards } from '@nestjs/common';
import { PermissionGuard } from './permission.guard';
import { AuthGuard } from './auth.guard';

// = 1
export function Permission(permissionName: string) {
  return applyDecorators(
    UseGuards(AuthGuard, new PermissionGuard(permissionName))
  );
}

// > 1
// export function Permission(...permissionNames: string[]) {
//   return applyDecorators(
//     UseGuards(AuthGuard, new PermissionGuard(permissionNames))
//   );
// }


// ตังอย่างการใช้งานใน Controller แบบตัวเดียว

// @Controller('forms')
// export class FormsController {
//   @Get()
//   @Permission('view_all_forms')
//   getForms() { ... }

//   @Post()
//   @Permission('create_form')
//   createForm() { ... }
// }


// ตังอย่างการใช้งานใน  หลายตัว

// @Get()
// @Permission('view_all_forms', 'edit_any_form')
// getForms() { ... }
