/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useQuizStore = defineStore('quiz', () => {
  // 🔹 State
  const meta = ref<QuizMeta | null>(null);
  const quizzes = ref<Quiz[]>([]);
  const currentQuiz = ref<Quiz | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // 🔹 Actions
  const fetchQuizzes = async () => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.getQuizzes({
        page: page.value,
        limit: limit.value,
        search: search.value,
      });

      quizzes.value = data.quizzes;
      meta.value = data.meta;
    } catch (err: any) {
      error.value = err?.message ?? 'เกิดข้อผิดพลาดในการโหลดแบบทดสอบ';
    } finally {
      loading.value = false;
    }
  };

  const fetchQuizById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.getQuizById(id);
      currentQuiz.value = data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  const addQuiz = async (quizData: Partial<Quiz>): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.createQuiz(quizData);
      currentQuiz.value = data;
      await fetchQuizzes();
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถเพิ่มแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuiz = async (id: number, quizData: Quiz): Promise<Quiz> => {
    const { data } = await quizService.updateQuiz(id, quizData);
    const index = quizzes.value.findIndex((q) => q.id === id);
    if (index !== -1) quizzes.value[index] = data;
    if (currentQuiz.value?.id === id) currentQuiz.value = data;
    return data;
  };

  const removeQuiz = async (id: number): Promise<void> => {
    await quizService.deleteQuiz(id);
    quizzes.value = quizzes.value.filter((q) => q.id !== id);
  };

  const previewQuiz = async (id: number): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.previewQuiz(id);
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถดูตัวอย่างแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuizField = async (id: number, field: string, value: unknown): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.updateQuizField(id, field, value);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถอัพเดตฟิลด์แบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuizTitle = async (id: number, title: string): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.updateQuizTitle(id, title);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถอัพเดตชื่อแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuizDescription = async (id: number, description: string): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.updateQuizDescription(id, description);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถอัพเดตคำอธิบายแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuizStatus = async (id: number, status: QuizStatus): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.updateQuizStatus(id, status);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถอัพเดตสถานะแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const publishQuiz = async (id: number): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.publishQuiz(id);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถเผยแพร่แบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const archiveQuiz = async (id: number): Promise<Quiz> => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await quizService.archiveQuiz(id);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = data;
      if (currentQuiz.value?.id === id) currentQuiz.value = data;
      return data;
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถเก็บถาวรแบบทดสอบได้';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    // state
    quizzes,
    currentQuiz,
    loading,
    error,
    meta,
    page,
    limit,
    search,

    // actions
    fetchQuizzes,
    fetchQuizById,
    addQuiz,
    updateQuiz,
    removeQuiz,
    previewQuiz,
    updateQuizField,
    updateQuizTitle,
    updateQuizDescription,
    updateQuizStatus,
    publishQuiz,
    archiveQuiz,
  };
});
