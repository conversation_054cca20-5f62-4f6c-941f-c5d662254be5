import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('questions')
export class Question {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  itemBlockId: number;

  @Column({ type: 'text' })
  questionText: string;

  @Column({ nullable: true, type: 'text' })
  imagePath: string;

  @Column({ nullable: true })
  imageWidth: number | null;

  @Column({ nullable: true })
  imageHeight: number | null;

  @Column({ default: false })
  isHeader: boolean;

  @Column()
  sequence: number;

  @Column({ nullable: true, default: null })
  sizeLimit: number;

  @Column({ nullable: true, default: null })
  acceptFile: string;

  @Column({ nullable: true, default: null })
  uploadLimit: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.questions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'itemBlockId' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.question, {
    cascade: true,
  })
  responses: Response[];

  @Column({ nullable: true })
  score: number | null;
}
