import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import type { AssessmentQueryParams, AssessmentType, DataResponse } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody, Question, Option } from 'src/types/models';
import { formatParams } from 'src/utils/utils';
import { Notify } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
import type { QuizAllResponsesData, QuizMetaResponse } from 'src/types/quiz';
const globalStore = useGlobalStore();

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
export class AssessmentService {
  path = '/assessments';
  type = 'quiz' as AssessmentType;
  headerBodyPath = '';

  constructor(type: AssessmentType) {
    this.type = type;
    this.headerBodyPath = '/header-bodies';
  }

  async fetchAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const format = formatParams(pag, search);
      const params = { ...format, type: this.type } as AssessmentQueryParams;
      const res = await axios.get<DataResponse<Assessment>>(this.path, { params });
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดรายการแบบประเมินได้');
      throw new Error('Fetch assessments failed');
    }
  }

  async fetchOne(id: number) {
    try {
      const res = await axios.get<Assessment>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูลแบบประเมินได้');
      throw new Error('Fetch single assessment failed');
    }
  }

  getFormByIdWithSec = async (id: number, section: number): Promise<{ data: Assessment }> => {
    try {
      return await axios.get<Assessment>(`${this.path}/preview/${id}/${section}`);
    } catch (error) {
      showError('ไม่สามารถโหลดฟอร์มแบบประเมินได้');
      throw error;
    }
  };

  async createOne(assessment: Partial<Assessment>) {
    try {
      const res = await axios.post<Assessment>(this.path, assessment);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถสร้างแบบประเมินได้');
      throw new Error('Create assessment failed');
    }
  }

  async updateOne(id: number, assessment: Partial<Assessment>) {
    try {
      const res = await axios.patch<Assessment>(`${this.path}/${id}`, assessment);
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตแบบประเมินได้');
      throw new Error('Update assessment failed');
    }
  }

  async duplicate(sourceId: number, targetId: number) {
    try {
      const res = await axios.patch<Assessment>(`${this.path}/${sourceId}/copy-to/${targetId}`);
      return res.data;
    } catch {
      showError('ไม่สามารถคัดลอกแบบประเมินได้');
      throw new Error('Duplicate assessment failed');
    }
  }

  async deleteOne(id: number) {
    try {
      const res = await axios.delete<Assessment>(`${this.path}/${id}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบแบบประเมินได้');
      throw new Error('Delete assessment failed');
    }
  }

  async createBlock(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    console.log('🚀 AssessmentService.createBlock called with:', {
      type: this.type,
      path: this.path,
      block: block,
    });

    // Use the correct endpoint directly based on the type
    if (this.type === 'evaluate') {
      // For evaluate type, use the correct endpoint: /item-blocks/block
      console.log('📍 Using evaluate endpoint: /item-blocks/block');
      return await this.createBlockViaCorrectEndpoint(block);
    } else {
      // For quiz type, try the original endpoint first
      try {
        console.log(
          '📍 Attempting to create block with quiz endpoint:',
          `${this.path}/item-blocks`,
        );
        console.log('Block data:', block);

        const res = await axios.post<ItemBlock>(`${this.path}/item-blocks`, block);
        globalStore.Loading();
        return res.data;
      } catch (error: unknown) {
        console.error('❌ Block creation failed:', error);

        // Type guard for axios error
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: unknown; status?: number } };
          console.error('Error response:', axiosError.response?.data);
          console.error('Error status:', axiosError.response?.status);

          // Check if it's a 404 error and try alternative endpoints
          if (axiosError.response?.status === 404) {
            console.log('Primary endpoint failed, trying alternative approaches...');
            return await this.createBlockFallback(block);
          } else {
            showError('ไม่สามารถสร้าง Block ได้');
          }
        } else {
          console.error('Full error:', error);
          showError('ไม่สามารถสร้าง Block ได้');
        }
        return;
      }
    }
  }

  // Fallback method to try alternative approaches
  private async createBlockFallback(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    // First try the simple /evaluate/item-blocks endpoint
    try {
      console.log('Trying simple evaluate endpoint: /evaluate/item-blocks');
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO',
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };
      console.log('Payload for simple endpoint:', payload);

      const res = await axios.post<ItemBlock>('/evaluate/item-blocks', payload);
      console.log('Success with simple endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Simple evaluate endpoint failed:', error);
    }

    console.log('Trying correct evaluate endpoint...');

    try {
      // Try the correct evaluate endpoint: /evaluate/item-blocks/block
      return await this.createBlockViaCorrectEndpoint(block);
    } catch (error) {
      console.error('Correct evaluate endpoint failed:', error);
    }

    console.log('Trying assessment update approach...');

    try {
      // Try using the assessment update approach as backup
      return await this.createBlockViaAssessmentUpdate(block);
    } catch (error) {
      console.error('Assessment update approach failed:', error);
    }

    // Try other alternative endpoints as last resort
    const alternatives = [
      `/evaluate/item-blocks`,
      `/item-blocks`,
      `/evaluate/item-blocks/create`,
      `/evaluate/blocks`,
      `/evaluate/blocks/create`,
      `${this.path.replace('/assessments', '')}/item-blocks`,
      `${this.path.replace('/assessments', '')}/blocks`,
    ];

    for (const endpoint of alternatives) {
      try {
        console.log(`Trying alternative endpoint: ${endpoint}`);
        const res = await axios.post<ItemBlock>(endpoint, block);
        console.log(`Success with endpoint: ${endpoint}`);
        globalStore.Loading();
        return res.data;
      } catch {
        console.log(`Failed with endpoint: ${endpoint}`);
        continue;
      }
    }

    // If all alternatives fail, show error
    showError(`ไม่พบ endpoint ที่ใช้งานได้สำหรับการสร้าง Block - กรุณาตรวจสอบ backend API`);
    return;
  }

  // Try creating block using the correct evaluate endpoint
  private async createBlockViaCorrectEndpoint(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    try {
      console.log('🎯 Attempting to create block via correct endpoint: /item-blocks/block');

      // Prepare the payload with required fields (based on backend logs)
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO', // Default to 'RADIO' as specified
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };

      console.log('📦 Payload for correct endpoint:', payload);

      // Use the exact endpoint that works from backend logs
      const res = await axios.post<ItemBlock>('/item-blocks/block', payload);
      console.log('✅ Success with correct endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Correct endpoint failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Error response from correct endpoint:', axiosError.response?.data);
        console.error('Error status from correct endpoint:', axiosError.response?.status);

        // If it's the header creation bug, don't try fallbacks for HEADER type
        if (block.type === 'HEADER' && axiosError.response?.status === 500) {
          console.error(
            '🚫 Header creation failed due to known backend bug - not trying fallbacks',
          );
          throw error; // Re-throw to let the caller handle it
        }
      }

      // Try fallback approaches for non-header blocks
      if (block.type !== 'HEADER') {
        console.log('🔄 Trying fallback approaches...');
        return await this.createBlockFallback(block);
      } else {
        throw error; // Re-throw for header blocks
      }
    }
  }

  // Try creating block by updating the assessment with new blocks
  private async createBlockViaAssessmentUpdate(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    if (!block.assessmentId) {
      throw new Error('Assessment ID is required for block creation');
    }

    console.log('Attempting to create block via assessment update...');

    // First, get the current assessment
    const currentAssessment = await this.fetchOne(block.assessmentId);
    console.log('Current assessment:', currentAssessment);

    // Create a new block with a temporary ID (backend should assign real ID)
    const tempId = Date.now(); // Temporary ID
    const newBlock: ItemBlock = {
      id: tempId,
      ...block,
    };

    // Add the new block to the assessment
    const updatedItemBlocks = [...(currentAssessment.itemBlocks || []), newBlock];

    const assessmentUpdate = {
      ...currentAssessment,
      itemBlocks: updatedItemBlocks,
    };

    console.log('Updating assessment with new block:', assessmentUpdate);

    // Update the assessment with the new block
    const updatedAssessment = await axios.patch<Assessment>(
      `${this.path}/${block.assessmentId}`,
      assessmentUpdate,
    );

    // Find the newly created block in the response
    const createdBlock = updatedAssessment.data.itemBlocks?.find(
      (b) => b.sequence === block.sequence && b.section === block.section && b.type === block.type,
    );

    if (createdBlock) {
      console.log('Block created successfully via assessment update:', createdBlock);
      globalStore.Loading();
      return createdBlock;
    } else {
      throw new Error('Block was not found in updated assessment');
    }
  }

  async addBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      console.log('➕ AssessmentService.addBlock called with:', {
        type: this.type,
        blockId: block.id,
        blockType: block.type,
      });

      let addEndpoint: string;

      if (this.type === 'evaluate') {
        // For evaluate type, use the correct endpoint: /item-blocks/{id}
        addEndpoint = `/item-blocks/${block.id}`;
        console.log('📍 Using evaluate add endpoint:', addEndpoint);
      } else {
        // For quiz type, use the original endpoint
        addEndpoint = `${this.path}/item-blocks/${block.id}`;
        console.log('📍 Using quiz add endpoint:', addEndpoint);
      }

      const res = await axios.put<ItemBlock>(addEndpoint, block);
      console.log('✅ Block added successfully:', res.data);

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block add failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Add error response:', axiosError.response?.data);
        console.error('Add error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถเพิ่ม Block ได้');
      return;
    }
  }

  async updateBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      console.log('🔄 AssessmentService.updateBlock called with:', {
        type: this.type,
        blockId: block.id,
        blockType: block.type,
      });

      let updateEndpoint: string;

      if (this.type === 'evaluate') {
        // For evaluate type, use the correct endpoint: /item-blocks/{id}
        updateEndpoint = `/item-blocks/${block.id}`;
        console.log('📍 Using evaluate update endpoint:', updateEndpoint);
      } else {
        // For quiz type, use the original endpoint
        updateEndpoint = `${this.path}/item-blocks/${block.id}`;
        console.log('📍 Using quiz update endpoint:', updateEndpoint);
      }

      const res = await axios.patch<ItemBlock>(updateEndpoint, block);
      console.log('✅ Block updated successfully:', res.data);

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block update failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Update error response:', axiosError.response?.data);
        console.error('Update error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถอัปเดต Block ได้');
      return;
    }
  }

  async deleteBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      console.log('🗑️ AssessmentService.deleteBlock called with:', {
        type: this.type,
        blockId: block.id,
        blockType: block.type,
        assessmentId: block.assessmentId,
        hasHeaderBody: !!block.headerBody,
        hasQuestions: !!block.questions?.length,
        hasOptions: !!block.options?.length,
        questionsCount: block.questions?.length || 0,
        optionsCount: block.options?.length || 0,
      });

      // Enhanced validation before deletion
      if (!block.id) {
        console.error('❌ Cannot delete block: Missing block ID');
        showError('ไม่สามารถลบได้ - ไม่พบ ID ของรายการ');
        return;
      }

      if (!block.assessmentId) {
        console.error('❌ Cannot delete block: Missing assessmentId');
        showError('ไม่สามารถลบได้ - ไม่พบ Assessment ID');
        return;
      }

      let deleteEndpoint: string;

      if (this.type === 'evaluate') {
        // For evaluate type, use the correct endpoint: /item-blocks/{id}
        deleteEndpoint = `/item-blocks/${block.id}`;
        console.log('📍 Using evaluate delete endpoint:', deleteEndpoint);
      } else {
        // For quiz type, use the original endpoint
        deleteEndpoint = `${this.path}/item-blocks/${block.id}`;
        console.log('📍 Using quiz delete endpoint:', deleteEndpoint);
      }

      console.log('🌐 Sending DELETE request to backend...', {
        endpoint: deleteEndpoint,
        blockId: block.id,
        blockType: block.type,
        timestamp: new Date().toISOString(),
      });

      const res = await axios.delete<ItemBlock>(deleteEndpoint);

      console.log('✅ Block deleted successfully from backend:', {
        deletedBlock: res.data,
        responseStatus: res.status,
        responseHeaders: res.headers,
        timestamp: new Date().toISOString(),
      });

      // Validate that the response contains the expected data
      if (!res.data || !res.data.id) {
        console.warn('⚠️ Backend response missing expected data:', res.data);
      } else if (res.data.id !== block.id) {
        console.warn('⚠️ Backend response ID mismatch:', {
          expectedId: block.id,
          receivedId: res.data.id,
        });
      }

      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block deletion failed:', error);

      // Enhanced error logging
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as {
          response?: { data?: unknown; status?: number; statusText?: string };
        };
        console.error('Delete error details:', {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          blockId: block.id,
          blockType: block.type,
          endpoint:
            this.type === 'evaluate'
              ? `/item-blocks/${block.id}`
              : `${this.path}/item-blocks/${block.id}`,
        });

        // Provide more specific error messages based on status code
        if (axiosError.response?.status === 404) {
          showError('ไม่พบรายการที่ต้องการลบ - อาจถูกลบไปแล้ว');
        } else if (axiosError.response?.status === 403) {
          showError('ไม่มีสิทธิ์ในการลบรายการนี้');
        } else if (axiosError.response?.status === 500) {
          showError('เกิดข้อผิดพลาดในระบบ - กรุณาลองใหม่อีกครั้ง');
        } else {
          showError('ไม่สามารถลบ Block ได้');
        }
      } else {
        console.error('Delete error (non-axios):', error);
        showError('ไม่สามารถลบ Block ได้');
      }

      return;
    }
  }

  // HeaderBody methods moved from HeaderBodyService
  async fetchHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.get<HeaderBody>(`${this.headerBodyPath}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูล Header Body ได้');
      return;
    }
  }

  async createHeaderBody(headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.post<HeaderBody>(this.headerBodyPath, headerBody);
      globalStore.Loading();
      console.log('HeaderBody created:', res.data);
      return res.data;
    } catch {
      showError('ไม่สามารถสร้าง Header Body ได้');
      return;
    }
  }

  async updateHeaderBody(
    id: number,
    headerBody: Partial<HeaderBody>,
  ): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.patch<HeaderBody>(`${this.headerBodyPath}/${id}`, headerBody);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Header Body ได้');
      return;
    }
  }

  async deleteHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.delete<HeaderBody>(`${this.headerBodyPath}/${id}`);
      Notify.create({ message: 'ลบ Header Body เรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบ Header Body ได้');
      return;
    }
  }

  // Question methods - using /questions/{id} endpoint following headerBody pattern
  async updateQuestion(id: number, question: Partial<Question>): Promise<Question | undefined> {
    try {
      const res = await axios.patch<Question>(`/questions/${id}`, question);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตคำถามได้');
      return;
    }
  }

  // Legacy method for backward compatibility - using /item-blocks/{id} endpoint with id and type
  async updateQuestionLegacy(
    itemBlockId: number,
    itemBlockType: string,
    question: Partial<Question>,
  ): Promise<Question | undefined> {
    try {
      const res = await axios.patch<Question>(`/item-blocks/${itemBlockId}`, {
        id: itemBlockId,
        type: itemBlockType,
        questionText: question.questionText,
      });
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตคำถามได้');
      return;
    }
  }

  // Option methods - using /item-blocks/{id} endpoint with id and type
  async updateOption(
    itemBlockId: number,
    itemBlockType: string,
    option: Partial<Option>,
  ): Promise<Option | undefined> {
    try {
      const res = await axios.patch<Option>(`/item-blocks/${itemBlockId}`, {
        id: itemBlockId,
        type: itemBlockType,
        optionText: option.optionText,
        score: option.value,
      });
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเデตตัวเลือกได้');
      return;
    }
  }
  async getMetaResponse(quizId: number): Promise<QuizMetaResponse> {
    try {
      const response = await axios.get<QuizMetaResponse>(`/quiz/assessments/${quizId}/meta`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching quiz meta for quizId ${quizId}:`, error);
      throw error;
    }
  }

  async getAllResponses(quizId: number): Promise<QuizAllResponsesData> {
    try {
      const response = await axios.get<QuizAllResponsesData>(
        `/quiz/assessments/${quizId}/response`,
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching all responses for quizId ${quizId}:`, error);
      throw error;
    }
  }
}
