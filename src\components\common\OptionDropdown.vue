<script setup lang="ts">
import { ref } from 'vue';
import { QSelect } from 'quasar';

const options = [
  { label: 'หลายตัวเลือก', value: 'multiple', icon: 'radio_button_checked' },
  { label: 'แบบเขียน', value: 'text', icon: 'notes' },
  { label: 'ช่องทำเครื่องหมาย', value: 'checkbox', icon: 'check_box' },
  { label: 'ตารางกริดหลายตัวเลือก', value: 'grid', icon: 'apps' },
];
const selected = ref('multiple');
</script>

<template>
  <div class="select-wrapper">
    <q-select
      v-model="selected"
      :options="options"
      filled
      dense
      emit-value
      map-options
      class="q-select-custom"
      input-class="q-select-input-custom"
      dropdown-icon="arrow_drop_down"
      popup-content-class="q-select-popup"
    >
      <template #option="scope">
        <q-item v-bind="scope.itemProps">
          <q-item-section avatar>
            <q-icon :name="scope.opt.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.label }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template #selected-item="scope">
        <q-item>
          <q-item-section avatar>
            <q-icon :name="scope.opt.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.label }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>

<style scoped>
.select-wrapper {
  position: relative;
  z-index: 1;
  padding: 10px;
}

.q-select-custom {
  width: 285px;
}

.q-select-input-custom {
  background: #ffffff;
  border: 1px solid #9d9d9d;
  border-radius: 12px;
  height: 45px;
  padding: 0 12px;
  box-sizing: border-box;
}

.q-select-popup {
  z-index: 10;
}
</style>
