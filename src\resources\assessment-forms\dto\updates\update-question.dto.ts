import { PartialType, ApiProperty } from '@nestjs/swagger';
import { CreateQuestionDto } from '../creates/create-question.dto';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateQuestionDto extends PartialType(CreateQuestionDto) {
  @ApiProperty({
    description: 'ID of the question to update',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  id?: number;
}
