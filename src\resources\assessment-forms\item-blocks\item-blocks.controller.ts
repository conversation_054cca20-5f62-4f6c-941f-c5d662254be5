import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import type { CreateItemBlockDto } from '../dto/creates/create-item-block.dto';
import type { UpdateItemBlockDto } from '../dto/updates/update-item-block.dto';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { ItemBlocksService } from './item-blocks.service';
import { UpdateItemBlockSequencesDto } from '../dto/updates/ีupdate-block-sequence.dto';

@ApiTags('Assessment-Item-Blocks')
@Controller('item-blocks')
export class ItemBlocksController {
  constructor(private readonly itemBlocksService: ItemBlocksService) {}

  @Post('block')
  @ApiOperation({
    summary: 'สร้าง Item Block ใหม่',
    description: 'สร้าง Item Block ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้าง Item Block ใหม่',
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: Object.values(ItemBlockType),
          example: ItemBlockType.RADIO,
        },
        sequence: {
          type: 'integer',
          example: 1,
          default: 1,
        },
        assessmentId: { type: 'integer', example: 1 },
      },
      required: ['assessmentId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() body: CreateItemBlockDto) {
    console.log(body);
    return this.itemBlocksService.createBlock(body);
  }

  async findAll(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
    @Query('page', ParseIntPipe) page: number,
  ) {
    return this.itemBlocksService.findAll(assessmentId, page);
  }

  @Get(':assessmentId/block')
  async findOne(@Param('assessmentId', ParseIntPipe) assessmentId: number) {
    return this.itemBlocksService.findOne(assessmentId);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัพเดท Item Block ใหม่',
    description: 'อัพเดท Item Block ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับอัพเดท Item Block ใหม่',
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: Object.values(ItemBlockType),
          example: ItemBlockType.RADIO,
        },
        sequence: {
          type: 'integer',
          example: 1,
          default: 1,
        },
      },
      required: ['type'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id') id: string,
    @Body() updateItemBlockDto: UpdateItemBlockDto,
  ) {
    return this.itemBlocksService.updateOne(+id, updateItemBlockDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.itemBlocksService.removeOne(id);
  }

  @Get(':id')
  async findItemOne(@Param('id', ParseIntPipe) itemBlockId: number) {
    return this.itemBlocksService.findItemOne(itemBlockId);
  }

  @Patch('update-sequences')
  updateSequences(@Body() updateSequencesDto: UpdateItemBlockSequencesDto) {
    return this.itemBlocksService.updateSequences(updateSequencesDto);
  }

  @Get('quiz/sequence/:submissionId/:sequence')
  sequenceQuestion(
    @Param('submissionId') submissionId: number,
    @Param('sequence') sequence: number,
  ) {
    return this.itemBlocksService.sequenceQuestion(submissionId, sequence);
  }
}
