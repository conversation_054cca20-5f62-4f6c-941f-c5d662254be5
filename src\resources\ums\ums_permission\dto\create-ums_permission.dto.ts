import { Max<PERSON>ength, <PERSON>NotEmpty, Matches } from 'class-validator';

export class CreateUmsPermissionDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches('^[ก-๛\\sก-๛]')
  perNameTh: string;

  @IsNotEmpty()
  @MaxLength(50)
  @Matches('^[A-Za-z\\sA-Za-z]')
  perNameEn: string;

  @IsNotEmpty()
  @MaxLength(100)
  updateUser: string;

  @IsNotEmpty()
  @MaxLength(100)
  updateProgram: string;
}
