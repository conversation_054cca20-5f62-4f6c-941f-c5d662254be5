import { Role } from 'src/resources/roles/entities/role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
} from 'typeorm';

@Entity('permissions')
export class Permission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  perNameTh: string;

  @Column()
  perNameEn: string;

  @Column({ default: true })
  perStatus: boolean;

  @Column({ default: false })
  perDefault: boolean;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];
}
