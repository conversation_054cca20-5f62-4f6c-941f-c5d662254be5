import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UmsPermissionService } from './ums_permission.service';
import { CreateUmsPermissionDto } from './dto/create-ums_permission.dto';
import { UpdateUmsPermissionDto } from './dto/update-ums_permission.dto';

@Controller('ums-permission')
export class UmsPermissionController {
  constructor(private readonly umsPermissionService: UmsPermissionService) {}

  @Post()
  create(@Body() createUmsPermissionDto: CreateUmsPermissionDto) {
    return this.umsPermissionService.create(createUmsPermissionDto);
  }

  @Get()
  findAll() {
    return this.umsPermissionService.findAll();
  }

  @Get('/actives')
  findAllActive() {
    return this.umsPermissionService.findAllActive();
  }

  @Get('/perms')
  findPerms(@Query() query) {
    return this.umsPermissionService.findPerms(query);
  }

  @Get(':perId')
  findOne(@Param('perId') perId: string) {
    return this.umsPermissionService.findOne(+perId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUmsPermissionDto: UpdateUmsPermissionDto,
  ) {
    return this.umsPermissionService.update(+id, updateUmsPermissionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.umsPermissionService.remove(+id);
  }
}
