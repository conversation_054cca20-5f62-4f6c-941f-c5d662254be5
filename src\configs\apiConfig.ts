export default () => ({
  domain: 'https://kong-dev.buu.ac.th',
  requestTokenAPI: {
    path: '/oauth2/token/',
    client_secret: 'WY8CsIgdaI9PfZfrvF2MvnDhM2X62Fvb',
    client_id: 'ExTYa2pFbFC8YI5WSkkF5etz8imZ1dA7',
    grant_type: 'password',
    authenticated_userid: 'e-office-kong',
  },
  refreshTokenAPI: {
    client_secret: 'WY8CsIgdaI9PfZfrvF2MvnDhM2X62Fvb',
    client_id: 'ExTYa2pFbFC8YI5WSkkF5etz8imZ1dA7',
    grant_type: 'refresh_token',
  },
  loginBuu: {
    path: '/service-api/ldap.loginBuu',
    provision_key: 'm4uWGFUSP39Wl7E0pRlEcvy4F75dCsJK',
    scope: 'read',
    secret: false,
  },
  sendEmail: {
    path: '/service-api/email.SendEmail',
    provision_key: 'BVoKpBlKLZqE9yvEeGs4h77Y0a5Ap2zb',
    scope: 'write',
  },
  uploadFile: {
    path: '/service-api/minio.PutFile',
    provision_key: 'l7hdoiyMMtelzqUJoXofCxI3m56CPXZ6',
    scope: 'write',
  },
  getFileList: {
    path: '/service-api/minio.GetFilesList',
    provision_key: 'is2hnNscNAImX8JOmDVAfTAUrXaXiEwx',
    scope: 'read',
  },
  getPublicFile: {
    path: '/service-api/minio.GetPublicFile',
    provision_key: 'DI3abB5mVqz1L3tibii1hrjuUo89lfcW',
    scope: 'read',
  },
  deleteFile: {
    path: '/service-api/minio.DeleteFile',
    provision_key: 'UV1yu0y4JaYl3WduIMNw8qMuk9SJSChD',
    scope: 'write',
  },
  noti: 'test ver** ',
  isDev: true, //isDev: false, for not show some on prod
  dsPrefix: {
    path: '/service-api/ds.Prefix',
    provision_key: 'ImBzXZmAEjIECWxdLxKFDAWS82eH2zJ9',
    scope: 'read',
  },
});
