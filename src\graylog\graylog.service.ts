import { Injectable, Logger } from '@nestjs/common';
import * as Gelf<PERSON><PERSON> from 'gelf-pro';
@Injectable()
export class GraylogService extends Logger {
  constructor() {
    super();
    GelfPro.setConfig({
      fields: { facility: 'intern-dev' },
      adapterName: 'tcp',
      adapterOptions: {
        host: '*********',
        port: 12201,
      },
    });
  }
  async error(message: string, error: string, sub: string) {
    const res = await this.sendLog(message, 3, {
      error: error,
      host: `intern-dev-${sub}`,
    });
    // console.log(res);
  }

  async info(
    message: string,
    event: string,
    parameter: any,
    status: boolean,
    sub: string,
  ) {
    const res = await this.sendLog(message, 6, {
      event: event,
      status: status,
      parameter: parameter,
      host: `intern-dev-${sub}`,
    });
    // console.log(res);
  }

  async warn(message: any, context?: string) {
    const res = await this.sendLog(`[${context || 'App'}] ${message}`, 4);
    // console.log(res);
  }

  async debug(message: any, context?: string) {
    const res = await this.sendLog(`[${context || 'App'}] ${message}`, 7);
    // console.log(res);
  }

  async verbose(message: any, context?: string) {
    const res = await this.sendLog(`[${context || 'App'}] ${message}`, 5);
    // console.log(res);
  }

  sendLog = (message, level, extras?) => {
    console.log(message, level, ': graylog');
    // return new Promise((resolve, reject) => {
    //   try {
    //     GelfPro.message(message, level, extras);
    //     resolve('Sent ' + message);
    //   } catch (err) {
    //     reject(err);
    //   }
    // });
  };
}
