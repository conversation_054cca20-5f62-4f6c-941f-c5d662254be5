import type { QuasarLanguage } from 'quasar';

const thaiQuasarLang: QuasarLanguage = {
  isoName: 'th-TH',
  nativeName: 'ภาษาไทย',
  label: {
    expand(label?: string): string {
      return `เปิด ${label}`;
    },
    collapse(label?: string): string {
      return `ปิด ${label}`;
    },
    clear: 'ล้าง',
    close: 'ปิด',
    cancel: 'ปิดเมนู',
    ok: 'ตกลง',
    search: 'ค้นหา',
    reset: 'รีเซ็ต',
    remove: 'ลบ',
    refresh: 'รีเฟรช',
    update: 'อัพเดท',
    create: 'สร้าง',
    select: 'เลือก',
    set: 'เซ็ต',
    filter: 'กรอง',
  },
  date: {
    days: ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'],
    daysShort: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],
    months: [
      'มกราคม',
      'กุมภาพันธ์',
      'มีนาคม',
      'เมษายน',
      'พฤษภาคม',
      'มิถุนายน',
      'กรกฎาคม',
      'สิงหาคม',
      'กันยายน',
      'ตุลาคม',
      'พฤศจิกายน',
      'ธันวาคม',
    ],
    monthsShort: [
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ],
    firstDayOfWeek: 0,
    format24h: true,
    pluralDay: 'หลายวัน',
    headerTitle: (date: Date, model: { year: number; month: number; day: number }): string => {
      return `${model.year}/${model.month}/${model.day}`;
    },
  },
  table: {
    noData: 'ไม่มีข้อมูล',
    noResults: 'ไม่พบผลลัพธ์ที่ตรงกัน',
    loading: 'กำลังโหลด...',
    columns: 'คอลัมน์',
    recordsPerPage: 'จำนวนแถวต่อหน้า',
    allRows: 'ทั้งหมด',
    selectedRecords(rows) {
      return rows === 1 ? '1 แถวถูกเลือก' : `${rows} แถวถูกเลือก`;
    },
    pagination(start, end, total) {
      return `${start}-${end} จาก ${total}`;
    },
  },
  editor: {
    url: 'URL',
    bold: 'ตัวหนา',
    italic: 'ตัวเอียง',
    strikethrough: 'ขีดกลาง',
    underline: 'ขีดเส้นใต้',
    unorderedList: 'รายการไม่เรียงลำดับ',
    orderedList: 'รายการเรียงลำดับ',
    subscript: 'ตัวห้อย',
    superscript: 'ตัวยก',
    hyperlink: 'ลิงก์',
    toggleFullscreen: 'เปิด/ปิดเต็มจอ',
    quote: 'อ้างอิง',
    left: 'จัดชิดซ้าย',
    center: 'จัดกึ่งกลาง',
    right: 'จัดชิดขวา',
    justify: 'จัดเต็มบรรทัด',
    print: 'พิมพ์',
    outdent: 'ลดย่อหน้า',
    indent: 'เพิ่มย่อหน้า',
    removeFormat: 'ลบรูปแบบ',
    formatting: 'รูปแบบ',
    fontSize: 'ขนาดตัวอักษร',
    align: 'จัดตำแหน่ง',
    hr: 'เส้นคั่น',
    undo: 'ย้อนกลับ',
    redo: 'ทำซ้ำ',
    heading1: 'หัวเรื่อง 1',
    heading2: 'หัวเรื่อง 2',
    heading3: 'หัวเรื่อง 3',
    heading4: 'หัวเรื่อง 4',
    heading5: 'หัวเรื่อง 5',
    heading6: 'หัวเรื่อง 6',
    paragraph: 'ย่อหน้า',
    code: 'โค้ด',
    size1: 'ขนาด 1',
    size2: 'ขนาด 2',
    size3: 'ขนาด 3',
    size4: 'ขนาด 4',
    size5: 'ขนาด 5',
    size6: 'ขนาด 6',
    size7: 'ขนาด 7',
    defaultFont: 'ฟอนต์เริ่มต้น',
    viewSource: 'ดูซอร์สโค้ด',
  },
  tree: { noNodes: 'ไม่มีข้อมูล', noResults: 'ไม่พบผลลัพธ์ที่ตรงกัน' },
};

export default thaiQuasarLang;
