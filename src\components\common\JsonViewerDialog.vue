<template>
  <q-dialog v-model="isOpenModel" persistent>
    <q-card style="min-width: 80vw; max-width: 90vw; min-height: 60vh; max-height: 80vh">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ title }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none" style="max-height: 70vh; overflow-y: auto">
        <q-scroll-area style="height: 100%; max-height: 60vh">
          <pre class="json-content">{{ formattedJson }}</pre>
        </q-scroll-area>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="Copy to Clipboard"
          color="primary"
          @click="copyToClipboard"
          icon="content_copy"
        />
        <q-btn flat label="Close" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useQuasar } from 'quasar';

const props = defineProps<{
  title: string;
  jsonData: unknown;
  isOpen: boolean;
}>();

const emit = defineEmits<{
  'update:isOpen': [value: boolean];
}>();

const $q = useQuasar();

const isOpenModel = computed({
  get: () => props.isOpen,
  set: (value: boolean) => emit('update:isOpen', value),
});

const formattedJson = computed(() => {
  try {
    return JSON.stringify(props.jsonData, null, 2);
  } catch (error) {
    return 'Error formatting JSON: ' + String(error);
  }
});

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value);
    $q.notify({
      type: 'positive',
      message: 'JSON copied to clipboard!',
      position: 'top',
    });
  } catch {
    $q.notify({
      type: 'negative',
      message: 'Failed to copy to clipboard',
      position: 'top',
    });
  }
};
</script>

<style scoped>
.json-content {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
}
</style>
