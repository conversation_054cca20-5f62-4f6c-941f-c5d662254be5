<template>
  <div class="row justify-center q-mt-xl">
    <q-card class="q-pa-xl custom-card">
      <div class="text-h4 q-mb-lg text-weight-bold">ตั้งค่า</div>

      <!-- แต่ละ block = row -->
      <div class="q-gutter-md column">
        <!-- Row 1 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">กำหนดขอบเขตเวลา</div>
            <div class="text-caption text-grey">
              กำหนดวันที่และเวลาเพื่อเปิด-ปิดแบบทดสอบแบบอัตโนมัติ
            </div>
          </div>

          <div class="row q-gutter-sm item-center" style="margin-top: 20px;">
            <SelectDate
              :model-value="selectedDate"
              label="เลือกวันเริ่มต้น"
              style="margin-right: 20px;"
            />
            <SelectDate
              :model-value="selectedDateEnd"
              label="เลือกวันสิ้นสุด"
              style="margin-right: 20px;"
              :disable="selectedDate === '' || selectedDate === null"
              :rules="[
                (val: any) => {
                  return val >= selectedDate || 'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น';
                },
              ]"
            />
          </div>
        </div>

        <!-- Row 2 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ตั้งเวลาทำแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเวลาในการทำแบบทดสอบของผู้ทำแบบสอบถาม</div>
          </div>
          <div class="row q-col-gutter-sm items-center">
            <HourDropdown :model-value="hour" />
            <MinDropdown :model-value="minute" style="margin-right: 30px" />
          </div>
        </div>

        <!-- Row 3 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">จำนวนครั้งที่สามารถทำแบบทดสอบ</div>
            <div class="text-caption text-grey">
              กำหนดจำนวนครั้งที่สามารถทำแบบทดสอบของผู้ทำแบบสอบถาม
            </div>
          </div>
          <div style="min-width: 300px">
            <TextField
              v-model:model-value="attemptLimit"
              placeholder="กรุณากรอกข้อมูล..."
              type="number"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ร้อยละขั้นต่ำเพื่อผ่านแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเกณฑ์คะแนนผ่านของแบบสอบถาม</div>
          </div>
          <div style="min-width: 300px">
            <TextField
              v-model:model-value="passPercentage"
              placeholder="กรุณากรอกข้อมูล..."
              type="number"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">เป็นต้นแบบ</div>
            <div class="text-caption text-grey">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div style="min-width: 80px">
            <Toggle v-model:model-value="isPrototype" />
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import SelectDate from 'src/components/common/SelectDate.vue';
import TextField from 'src/components/common/TextField.vue';
import HourDropdown from 'src/components/common/HourDropdown.vue';
import MinDropdown from 'src/components/common/MinDropdown.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';

const route = useRoute();
const paramId = route.params.id as string;
const assessment = new AssessmentService('quiz');
const assessmentStore = useAssessmentStore();

const selectedDate = ref('');
const selectedDateEnd = ref('');
const hour = ref<number | null>(null);
const minute = ref<number | null>(null);
const attemptLimit = ref<number>();
const passPercentage = ref<number>();
const isPrototype = ref(false);

const convertSecondsToHourMinute = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return { hours, minutes };
};

// const convertToSeconds = (hours: number, minutes: number) => {
//   return (hours * 3600) + (minutes * 60);
// };

onMounted(async () => {
  const response = await assessment.fetchOne(Number(paramId));
  console.log('Assessment fetched:', assessment.fetchOne(Number(paramId)));
  assessmentStore.setCurrentAssessment(response);
  if (assessmentStore.currentAssessment) {
    selectedDate.value = assessmentStore.currentAssessment.startAt || '';
    selectedDateEnd.value = assessmentStore.currentAssessment.endAt || '';

    if (response.timeout) {
      const { hours, minutes } = convertSecondsToHourMinute(response.timeout);
      hour.value = hours;
      minute.value = minutes;
    }

    attemptLimit.value = assessmentStore.currentAssessment.submitLimit;
    passPercentage.value = assessmentStore.currentAssessment.passRatio;
    isPrototype.value = assessmentStore.currentAssessment.isPrototype || false;
  }
});

onUnmounted(() => {
  assessmentStore.clearCurrentAssessment();
});
</script>

<style scoped>
.custom-card {
  width: 1100px;
  height: 600px;

  border-radius: 12px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>
