<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: number | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | null): void;
}>();

const selectedMinute = ref<number | null>(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    selectedMinute.value = newValue;
  },
);

watch(selectedMinute, (val) => {
  emit('update:modelValue', val);
});

const minute = Array.from({ length: 59 }, (_, i) => ({
  label: `${i + 1}`,
  value: i + 1,
}));
</script>

<template>
  <q-select
    v-model="selectedMinute"
    :options="minute"
    emit-value
    map-options
    outlined
    dense
    class="custom-select"
    dropdown-icon="arrow_drop_down"
  >
    <template #selected>
      <span class="custom-placeholder" v-if="selectedMinute === null">นาที</span>
      <span v-else>{{ selectedMinute }}</span>
    </template>
  </q-select>
</template>

<style scoped>
.custom-select {
  width: 100px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
}

.custom-select :deep(.q-field__control) {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-radius: 12px;
  border: 1px solid #bdbdbd;
  background-color: #fff;
}

.custom-select :deep(.q-field__native) {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
  margin: 0;
}

.custom-placeholder {
  color: #9e9e9e;
  font-size: 14px;
}
</style>
