import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import axios from 'axios';
import ApiToken from 'src/types/api/apiToken';
import ApiConfig from 'src/types/api/apiConfig';
import RequestTokenApiConfig from 'src/types/api/requestTokenApiConfig';
import { GraylogService } from 'src/graylog/graylog.service';
import { v4 as uuidv4 } from 'uuid';
import { unlink, createReadStream } from 'fs';
import { promisify } from 'util';
import { UploadFileDto } from 'src/types/api/uploadFile';

@Injectable()
export class ApiService {
  constructor(
    private configService: ConfigService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private graylogService: GraylogService,
  ) {}

  async postApi(apiName: string, data: any) {
    return this.makeApiRequest('post', 'postApi', apiName, data);
  }

  async getApi(apiName: string) {
    return this.makeApiRequest('get', 'getApi', apiName);
  }

  async deleteApi(apiName: string, data: any) {
    return this.makeApiRequest('delete', 'deleteApi', apiName, data);
  }

  async makeApiRequest(
    method: string,
    funcName: string,
    apiName: string,
    data?: any,
  ) {
    const apiConfig: ApiConfig = this.configService.get<ApiConfig>(apiName);
    const cacheToken = await this.getCacheToken(apiName);

    const url = `${this.configService.get<string>('domain')}${apiConfig.path}`;
    const headers = {
      Accept: 'application/json',
      Authorization: `Bearer ${cacheToken.access_token}`,
      'x-forwarded-proto': 'https',
    };

    try {
      let response;
      switch (method) {
        case 'post':
          response = await axios.post(url, data, { headers }); //
          break;
        case 'get':
          response = await axios.get(url, { headers });
          break;
        case 'delete':
          response = await axios.delete(url, { headers, data });
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      return response;
    } catch (error) {
      return this.handleApiError(apiName, error, data, method, funcName);
    }
  }

  async getCacheToken(apiName: string): Promise<ApiToken> {
    let cacheToken = await this.cacheManager.get<ApiToken>(
      `${apiName}_apiToken`,
    );
    if (!cacheToken) {
      await this.requestToken(apiName);
      cacheToken = await this.cacheManager.get<ApiToken>(`${apiName}_apiToken`);
    }
    return cacheToken;
  }

  async handleApiError(
    apiName: string,
    error: any,
    data: any,
    method: string,
    funcName: string,
  ) {
    const eflog = {
      efLogRequest: `Request... ${method} /api/${apiName}`,
      efLogRequestBody: data ? JSON.stringify(data) : null,
      efLogResStatusCode: error.response?.status?.toString() || '',
      efLogResMessage: error.message || '',
      efLogReqUser: 'x',
    };
    await this.graylogService.error(
      `${eflog.efLogRequest} ${eflog.efLogResMessage}`,
      eflog.efLogRequestBody,
      'from_web',
    );
    if (
      error.response?.data.error === 'invalid_token' ||
      error.response?.status === 401
    ) {
      await this.refreshToken(apiName);
      console.log('redo ', funcName);
      if (typeof this[funcName] === 'function') {
        return this[funcName](apiName, data);
      } else {
        console.error(`Method ${funcName} is not a valid function`);
        return error.response?.data;
      }
    } else {
      await this.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error.message}`,
      );
      return error.response?.data;
    }
  }

  async refreshToken(apiName: string) {
    try {
      console.log('refreshToken');
      const cacheToken: ApiToken = await this.cacheManager.get(
        `${apiName}_apiToken`,
      );
      const requestTokenConfig: RequestTokenApiConfig =
        await this.configService.get<RequestTokenApiConfig>('requestTokenAPI');
      const apiConfig: ApiConfig =
        await this.configService.get<ApiConfig>(apiName);

      const response = await axios.post(
        `${this.configService.get<string>('domain')}${apiConfig.path}${requestTokenConfig.path}`,
        {
          client_secret: requestTokenConfig.client_secret,
          client_id: requestTokenConfig.client_id,
          grant_type: 'refresh_token',
          refresh_token: cacheToken.refresh_token,
        },
        {
          headers: { 'x-forwarded-proto': 'https' },
        },
      );

      await this.cacheManager.set(`${apiName}_apiToken`, response.data, 0);
    } catch (error) {
      return this.handleApiError(apiName, error, {}, 'post', 'refreshToken');
    }
  }

  async requestToken(apiName: string) {
    try {
      console.log('requestToken ' + apiName);
      const requestTokenConfig: RequestTokenApiConfig =
        await this.configService.get<RequestTokenApiConfig>('requestTokenAPI');
      const apiConfig: ApiConfig =
        await this.configService.get<ApiConfig>(apiName);

      const response = await axios.post(
        `${this.configService.get<string>('domain')}${apiConfig.path}${requestTokenConfig.path}`,
        {
          client_secret: requestTokenConfig.client_secret,
          client_id: requestTokenConfig.client_id,
          grant_type: requestTokenConfig.grant_type,
          scope: apiConfig.scope,
          provision_key: apiConfig.provision_key,
          authenticated_userid: requestTokenConfig.authenticated_userid,
        },
        { timeout: 120000 },
      );

      await this.cacheManager.set(`${apiName}_apiToken`, response.data, 0);
    } catch (error) {
      return this.handleApiError(apiName, error, {}, 'post', 'requestToken');
    }
  }

  async sendEmail(
    system: string,
    to: string,
    subject: string,
    message: string,
  ) {
    const apiName = 'sendEmail';
    const data = {
      send_system: system,
      send_to:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? '<EMAIL>'
          : to,
      send_cc: process.env.AUTOMATE_TEST_MODE == 'true' ? '' : to,
      send_subject: subject,
      send_message: message,
    };
    try {
      const response = await this.postApi(apiName, data);
      return response.data;
    } catch (error) {
      await this.sendNoti(
        this.configService.get<string>('noti') + apiName + ': ' + error,
      );
      console.log(error);
    }
  }

  async sendEmailCC(
    system: string,
    to: string,
    cc: string,
    subject: string,
    message: string,
  ) {
    const apiName = 'sendEmail';
    const data = {
      send_system: system,
      send_to:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? '<EMAIL>'
          : to,
      send_cc: process.env.AUTOMATE_TEST_MODE == 'true' ? '' : cc,
      send_subject: subject,
      send_message: message,
    };
    try {
      const response = await this.postApi(apiName, data);
      return response.data;
    } catch (error) {
      await this.sendNoti(
        this.configService.get<string>('noti') + apiName + ': ' + error,
      );
      console.log(error);
    }
  }

  async uploadFile(
    file: Express.Multer.File,
    uploadFileDto: UploadFileDto,
  ): Promise<any> {
    const apiName = 'uploadFile';
    if (
      !uploadFileDto.path ||
      uploadFileDto.path === '' ||
      uploadFileDto.path === 'undefined' ||
      uploadFileDto.path === undefined
    ) {
      uploadFileDto.path = '/uploaded_files';
    }
    const apiConfig: ApiConfig = this.configService.get<ApiConfig>(apiName);
    const cacheToken = await this.getCacheToken(apiName);
    const dataObj = this.constructFileData(file, uploadFileDto);
    try {
      dataObj.fileUpload = await this.readFile(file.path);
      const response = await this.uploadToApi(apiConfig, cacheToken, dataObj);
      await this.cleanupFile(file.path);
      return response.data;
    } catch (error) {
      return this.handleUploadError(
        error,
        dataObj,
        apiName,
        file,
        uploadFileDto,
      );
    }
  }

  constructFileData(file: Express.Multer.File, uploadFileDto: UploadFileDto) {
    const list = file.filename ? file.filename.split('.') : [];
    const fileType =
      uploadFileDto.fileType && uploadFileDto.fileType != 'undefined'
        ? uploadFileDto.fileType
        : list[list.length - 1] || '';
    const name = uuidv4();
    const fileName = uploadFileDto.fileName?.includes('ums_system_')
      ? uploadFileDto.fileName
      : `${name}.${fileType}`;

    const dataObj: any = {
      path: uploadFileDto.path,
      fileName: fileName,
      bucket: 'intern.dev.storage',
      originalExtension: fileType,
    };

    if (uploadFileDto?.qrVerify == 'true') {
      dataObj.qrVerify = 'true';
    }

    return dataObj;
  }

  async readFile(filePath: string) {
    try {
      return createReadStream(filePath);
    } catch (error) {
      console.error('Error reading file:', error);
      throw new Error('Error reading file');
    }
  }

  async uploadToApi(apiConfig: ApiConfig, cacheToken: ApiToken, dataObj: any) {
    return axios.post(
      `${this.configService.get<string>('domain')}${apiConfig.path}`,
      dataObj,
      {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${cacheToken.access_token}`,
          'Content-Type': 'multipart/form-data',
          'x-forwarded-proto': 'https',
        },
      },
    );
  }

  async cleanupFile(filePath: string) {
    const unlinkAsync = promisify(unlink);
    await unlinkAsync(filePath);
  }

  async handleUploadError(
    error: any,
    dataObj: any,
    apiName: string,
    file: Express.Multer.File,
    uploadFileDto: UploadFileDto,
  ) {
    console.log(error.response?.data);

    const eflog = {
      efLogRequest: `Request... POST /api/${apiName}`,
      efLogRequestBody: JSON.stringify(dataObj),
      efLogResStatusCode: error.response?.status.toString() || '',
      efLogResMessage: error.message || '',
      efLogReqUser: 'x', // Replace with user info if necessary
    };

    await this.graylogService.error(
      `${eflog.efLogRequest} ${eflog.efLogResMessage}`,
      eflog.efLogRequestBody,
      'from_web',
    );

    if (
      error.response?.data.error === 'invalid_token' ||
      error.response?.status === 401
    ) {
      await this.refreshToken(apiName);
      return this.uploadFile(file, uploadFileDto);
    } else {
      await this.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error.message}`,
      );
      return error.response?.data;
    }
  }

  async getFileList(path: string) {
    const apiName = 'getFileList';
    const data = {
      path: path || '/uploaded_files',
      bucket: 'intern.dev.storage',
    };
    try {
      const response = await this.postApi(apiName, data);
      return response.data;
    } catch (error) {
      await this.sendNoti(
        this.configService.get<string>('noti') + apiName + ': ' + error,
      );
      console.log(error);
    }
  }

  async getPublicFile(fileName: string, downloadName?: string) {
    const apiName = 'getPublicFile';
    const data = {
      filePath: {
        file_1: fileName,
      },
      time: 7, // 7 D | 10080 M
      carbonTime: 'D',
      bucket: 'intern.dev.storage',
      originalFileName: {
        file_1: downloadName || fileName,
      },
    };
    try {
      const response = await this.postApi(apiName, data);
      return response.data;
    } catch (error) {
      if (
        error.response?.data.error === 'invalid_token' ||
        error.response?.status == 401
      ) {
        await this.refreshToken(apiName);
        return this.getPublicFile(fileName);
      } else {
        await this.sendNoti(
          this.configService.get<string>('noti') + apiName + ': ' + error,
        );
        console.log(error);
      }
    }
  }

  async getPublicFiles(files: { fileName: string; downloadName?: string }[]) {
    const apiName = 'getPublicFile';
    const filePath = {};
    const originalFileName = {};

    files.forEach((file, index) => {
      filePath[`file_${index + 1}`] = file.fileName;
      originalFileName[`file_${index + 1}`] =
        file.downloadName || file.fileName;
    });

    const data = {
      filePath,
      time: 7, // 7 D | 10080 M
      carbonTime: 'D',
      bucket: 'intern.dev.storage',
      originalFileName,
    };
    try {
      const response = await this.postApi(apiName, data);
      return response.data;
    } catch (error) {
      await this.sendNoti(
        this.configService.get<string>('noti') + apiName + ': ' + error,
      );
      console.log(error);
    }
  }

  async deleteFile(fileName: string) {
    const apiName = 'deleteFile';
    const path = fileName ? (fileName != 'none' ? fileName : '') : '';
    const data = {
      filePath: path,
      bucket: 'intern.dev.storage',
    };
    try {
      const response = await this.deleteApi(apiName, data);
      return response.data;
    } catch (error) {
      await this.sendNoti(
        this.configService.get<string>('noti') + apiName + ': ' + error,
      );
      console.log(error);
    }
  }

  async dsPrefix() {
    const apiName = 'dsPrefix';
    try {
      const response = await this.getApi(apiName);
      return response.data;
    } catch (error) {
      console.log(error);
    }
  }

  async sendNoti(message: string) {
    // try {
    //     await axios.post(
    //       'https://api.telegram.org/bot7682472342:AAHDC3vEasGWLNah3wJkcHGaNIhi21TxA8Y/sendMessage',
    //       {
    //         chat_id: '-4522198775',
    //         text: message?.substring(0, 1000),
    //       },
    //       {
    //         headers: {
    //           'Content-Type': 'application/x-www-form-urlencoded',
    //         },
    //       },
    //     );
    //   return response.data;
    // } catch (error) {
    //   console.log(error);
    // }
  }
}
