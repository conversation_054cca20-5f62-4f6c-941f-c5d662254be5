import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Permission } from './entities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async create(createPermissionDto: CreatePermissionDto) {
    if (typeof createPermissionDto.perStatus === 'string') {
      createPermissionDto.perStatus = createPermissionDto.perStatus === 'true';
    }
    if (typeof createPermissionDto.perDefault === 'string') {
      createPermissionDto.perDefault = createPermissionDto.perDefault === 'true';
    }
    const permission = this.permissionRepository.create(createPermissionDto);
    return this.permissionRepository.save(permission);
  }

  async findAll() {
    return this.permissionRepository.find();
  }

  async findAllByStatus(perStatus: boolean) {
    return this.permissionRepository.find({ where: { perStatus } });
  }
  
  async findOne(id: number) {
    const permission = await this.permissionRepository.findOne({ where: { id } });
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }
    return permission;
  }

  async setPermissionStatus(id: number, status: boolean) {
    const permission = await this.findOne(id);
    if (typeof status === 'string') {
      status = status === 'true';
    }
    permission.perStatus = status;
    return this.permissionRepository.save(permission);
  }

  async update(id: number, updatePermissionDto: UpdatePermissionDto) {
    const permission = await this.findOne(id);
    if (typeof updatePermissionDto.perStatus === 'string') {
      updatePermissionDto.perStatus = updatePermissionDto.perStatus === 'true';
    }
    if (typeof updatePermissionDto.perDefault === 'string') {
      updatePermissionDto.perDefault = updatePermissionDto.perDefault === 'true';
    }
    const updated = { ...permission, ...updatePermissionDto };
    return this.permissionRepository.save(updated);
  }

  async remove(id: number) {
    const permission = await this.findOne(id);
    return this.permissionRepository.remove(permission);
  }

}
