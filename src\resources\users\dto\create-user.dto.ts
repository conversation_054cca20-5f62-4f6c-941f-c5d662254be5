import {
  IsS<PERSON>,
  IsNotEmpty,
  IsEmail,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength,
  IsArray,
  IsInt,
  IsOptional,
} from 'class-validator';

export class CreateUserDto {
  @IsString()
  // @IsNotEmpty()
  // @MaxLength(100)
  name: string;

  // @IsEmail({}, { message: 'Please provide a valid email address.' })
  @IsNotEmpty()
  // @MaxLength(100)
  email: string;

  // @IsString()
  @IsNotEmpty()
  // @MinLength(8, { message: 'Password must be at least 8 characters long.' })
  password: string;

  @IsArray()
  @IsInt({ each: true })
  @IsOptional()
  roleIds?: number[];
}
