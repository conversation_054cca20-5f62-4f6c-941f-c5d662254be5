<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useSystemPermStore } from 'src/stores/ums/systemPerm';
import type SystemPerm from 'src/types/ums/systemPerm';
// import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import { useUtilsStore } from 'src/stores/utils';

const router = useRouter();
const systemPermStore = useSystemPermStore();
const utilsStore = useUtilsStore();

const isLoading = ref(true);
const confirmDlg = ref();
// const search = ref('')
const permName = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(0);
const systemPerms = ref<SystemPerm[]>([]);
const selectedSystemPerm = ref<SystemPerm>();

const statusSwitchLoading = ref<{ [key: number]: boolean }>({});
const defaultSwitchLoading = ref<{ [key: number]: boolean }>({});

const getSystemPerms = async () => {
  isLoading.value = true;
  const data = await systemPermStore.getAllSystemPerms(
    {
      rowsPerPage: itemsPerPage.value,
      sortBy: 'perId',
    },
    {
      permName: permName.value,
    },
  );
  systemPerms.value = data.data;
  totalItems.value = data.total;
  currentPage.value = data.currentPage;
  isLoading.value = false;
};

watch(permName, () => {
  void getSystemPerms();
});

const AddPage = () => router.push({ name: 'ums_permission_add' });
const toEditPage = (perId: number) =>
  router.push({
    name: 'ums_permission_edit',
    params: { perId: utilsStore.encryptString('editId', perId.toString(), true) },
  });

const confirmDelete = async () => {
  await systemPermStore.deleteSystemPerm(selectedSystemPerm.value!.perId!);
  selectedSystemPerm.value = undefined;
  void getSystemPerms();
};

const deleteSystemPerm = async () => {
  try {
    await confirmDlg.value.openDialog(
      'ยืนยันการลบข้อมูล',
      'warning',
      'คุณต้องการลบข้อมูลสิทธิ์',
      `${selectedSystemPerm.value?.perNameTh}`,
      'ใช่หรือไม่',
      'ยืนยัน',
      'ยกเลิก',
    );
    await confirmDelete();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    // canceled
  }
};

const changeSystemPermStatus = async (systemPerm: SystemPerm) => {
  statusSwitchLoading.value[systemPerm.perId!] = true;
  await systemPermStore.editSystemPerm(systemPerm);
  statusSwitchLoading.value[systemPerm.perId!] = false;
};

const changeSystemPermDefault = async (systemPerm: SystemPerm) => {
  defaultSwitchLoading.value[systemPerm.perId!] = true;
  await systemPermStore.editSystemPerm(systemPerm);
  defaultSwitchLoading.value[systemPerm.perId!] = false;
};

const onClickDeleteItem = async (item: SystemPerm) => {
  selectedSystemPerm.value = item;
  await deleteSystemPerm();
};
</script>

<template>
  <q-page class="q-pa-md fontSarabun">
    <q-breadcrumbs class="q-mb-md">
      <q-breadcrumbs-el
        label="pre-system"
        icon="chevron_right"
        @click="router.push({ name: 'home' })"
      />
      <q-breadcrumbs-el label="จัดการข้อมูลสิทธิ์ในระบบ" icon="chevron_right" />
      <q-breadcrumbs-el label="จัดการข้อมูลสิทธิ์" icon="chevron_right" />
    </q-breadcrumbs>

    <q-card flat bordered>
      <q-card-section class="row items-center q-px-md">
        <div class="col-12 col-md-8 text-h6">จัดการข้อมูลสิทธิ์</div>
        <div class="col-12 col-md-4 text-right">
          <q-input dense debounce="300" v-model="permName" placeholder="ค้นหา" clearable>
            <template #append><q-icon name="search" /></template>
          </q-input>
          <q-btn color="primary" label="เพิ่ม" icon="add" @click="AddPage" class="q-ml-sm" />
        </div>
      </q-card-section>

      <q-markup-table separator="horizontal" flat class="q-ma-md">
        <thead>
          <tr>
            <th class="text-center">รหัส</th>
            <th class="text-center">ชื่อสิทธิ์ (ท.)</th>
            <th class="text-center">ชื่อสิทธิ์ (อ.)</th>
            <th class="text-center">แสดง/ไม่แสดง</th>
            <th class="text-center">สิทธิ์ตั้งต้น</th>
            <th class="text-center">เครื่องมือ</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in systemPerms" :key="item.perId ?? `undefined-${Math.random()}`">
            <td class="text-center">{{ item.perId }}</td>
            <td>{{ item.perNameTh }}</td>
            <td>{{ item.perNameEn }}</td>
            <td class="text-center">
              <q-toggle
                v-model="item.perStatus"
                true-value="Y"
                false-value="N"
                :loading="statusSwitchLoading[item.perId!]"
                @update:model-value="() => changeSystemPermStatus(item)"
              />
            </td>
            <td class="text-center">
              <q-toggle
                v-model="item.perDefault"
                true-value="Y"
                false-value="N"
                :loading="defaultSwitchLoading[item.perId!]"
                @update:model-value="() => changeSystemPermDefault(item)"
              />
            </td>
            <td class="text-center">
              <q-btn flat icon="edit" color="primary" @click="toEditPage(item.perId!)" />
              <q-btn flat icon="delete" color="negative" @click="onClickDeleteItem(item)" />
            </td>
          </tr>
        </tbody>
      </q-markup-table>

      <q-pagination
        v-model="currentPage"
        :max="Math.ceil(totalItems / itemsPerPage)"
        max-pages="7"
        class="q-pa-md"
      />
    </q-card>

    <!-- <ConfirmDialog ref="confirmDlg" /> -->
  </q-page>
</template>

<style scoped></style>
