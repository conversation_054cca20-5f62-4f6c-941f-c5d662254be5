<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserTextBlock
          v-if="evaluateItem.headerBody && evaluateItem.type === 'HEADER'"
          :item="evaluateItem"
          :title="evaluateItem.headerBody.title || 'ไม่มีข้อมูล'"
          :description="evaluateItem.headerBody.description || 'ไม่มีข้อมูล'"
        />
      </div>

      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserQuestionBlock
          v-if="evaluateItem.type !== 'IMAGE'"
          :id="evaluateItem.id"
          :draftId="submission?.id || 0"
          :item="editFormData"
          :category="evaluateItem.type"
          :section="evaluateItem.section"
          :status="isPreview"
          @update-answer="handleAnswer"
        />
        <UserImageBlock
          v-if="evaluateItem.type === 'IMAGE'"
          :title="evaluateItem.imageBody?.imageText || ''"
          :image-url="evaluateItem.imageBody?.imagePath || ''"
          @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
          @update:image-url="evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)"
          aria-label="dsda"
        />
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <q-btn v-if="isPreview" label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <q-btn v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <q-btn v-show="hideButton" label="หน้าต่อไป" @click="nextSection" />
      <div class="col-auto">
        <q-btn v-if="!hideButton && isPreview" label="ส่งแบบสอบถาม" @click="submitForm" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import { onMounted, ref, watch, watchEffect } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { summaryService } from 'src/services/asm/submissionService'; // หรือ path ที่คุณวางไฟล์จริงๆ
import type { Assessment, Submission, User } from 'src/types/models';
import { useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import router from 'src/router';

const route = useRoute();
const editFormData = ref<Assessment>();
const checkPage = ref<Assessment>();
const isPreview = ref(true);
const evaluateId = ref(0);
const currentSection = ref(1);

watch(
  () => route.params,
  (params) => {
    evaluateId.value = Number(params.id);
    if (String(route.params.status) === 'preview') {
      isPreview.value = false;
    } else if (String(route.params.status) === 'do') {
      isPreview.value = true;
    }
  },
  { immediate: true },
);

watch(
  () => route.params,
  async (params) => {
    const id = Number(params.id);
    const section = Number(params.section);

    if (!id || !section) return;

    evaluateId.value = id;
    currentSection.value = section;
    try {
      const res = await new AssessmentService('evaluate').getFormByIdWithSec(
        Number(route.params.id),
        currentSection.value,
      );
      editFormData.value = res.data;
    } catch (e) {
      console.error('โหลดข้อมูลไม่สำเร็จ', e);
    }
  },
  { immediate: true },
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number>;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
});

const hideButton = ref(true);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  const res = await new AssessmentService('evaluate').getFormByIdWithSec(
    Number(route.params.id),
    currentSection.value,
  );
  editFormData.value = res.data;
  if (String(route.params.status) === 'do') {
    await router.push({
      name: 'evaluate-do',
      params: {
        id: Number(route.params.id),
        status: 'do',
        section: currentSection.value,
      },
    });
  } else {
    await router.push({
      name: 'evaluate-preview',
      params: {
        id: Number(route.params.id),
        status: 'preview',
        section: currentSection.value,
      },
    });
  }

  const check = await new AssessmentService('evaluate').getFormByIdWithSec(
    Number(route.params.id),
    currentSection.value + 1,
  );
  checkPage.value = check.data;
  if (!checkPage.value) {
    hideButton.value = false;
  }
};

const nextSection = async () => {
  await updateSection(currentSection.value + 1);
};

const previousSection = async () => {
  if (currentSection.value > 1) {
    currentSection.value--;
    await updateSection(currentSection.value);
  }
};
const STORAGE_KEY = 'draft-form';
const clearForm = () => {
  localStorage.removeItem(STORAGE_KEY); // ลบค่าที่เก็บไว้
};

const submitForm = async () => {
  if (user.value && submission.value) {
    await summaryService.update(submission.value.id, {
      id: submission.value.id, // เผื่อจำเป็น
      assessmentId: submission.value.assessmentId,
      userId: user.value.id,
      startAt: submission.value.startAt,
      endAt: new Date().toISOString(),
    });
  }
};

//Submittion
const submission = ref<Submission>();
const user = ref<User>();
const authStore = useAuthStore();
onMounted(async () => {
  user.value = authStore.getCurrentUser();
  if (String(route.params.status) === 'do' && user.value) {
    const sub = await summaryService.getDraft(Number(route.params.id), user.value?.id);
    submission.value = sub;
    if (submission.value) {
      console.log('draft');
    } else {
      await summaryService.create({
        id: 0, // หรือไม่ต้องใส่ถ้า backend สร้าง id ให้อัตโนมัติ
        assessmentId: Number(route.params.id),
        userId: user.value?.id,
        startAt: '',
        endAt: '',
      });
    }
  }
});
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>
