<template>
  <q-page>
    <div v-for="i in 100" :key="i" class="q-mb-md">
      <HeaderBlock @blur="handleAutoSave" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import HeaderBlock from 'src/components/common/HeaderBlock.vue';

function handleAutoSave(fieldType: string, content: string) {
  // Your auto-save logic here
  console.log(`Auto-saving ${fieldType}: ${content}`);
  // e.g., saveQuiz(), or call an API directly
}
</script>

<style scoped></style>
