<script setup lang="ts">
import AppBreadcrumb from 'src/components/AppBreadcrumb.vue';
import MainFooter from 'src/components/MainFooter.vue';
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { useGlobalStore } from 'src/stores/global';
import MainHeader from 'src/components/MainHeader.vue';
import LeftDrawer from 'src/components/common/LeftDrawer.vue';
import { allDrawerMenu } from 'src/data/menu';

const route = useRoute();
const globalStore = useGlobalStore();

const fullBreadcrumbItems = computed(() => globalStore.getBreadcrumbByType('quiz'));

const breadcrumbItems = computed(() => {
  const currentPath = route.path;
  const result = [];

  if (fullBreadcrumbItems.value.length > 0) {
    const home = fullBreadcrumbItems.value[0];
    if (home) result.push(home);
  }

  for (let i = 1; i < fullBreadcrumbItems.value.length; i++) {
    const item = fullBreadcrumbItems.value[i];
    if (currentPath === item?.link) {
      result.push(item);
      break;
    }
    if (item?.link && currentPath.startsWith(item.link)) {
      result.push(item);
    }
  }

  return result;
});
</script>

<template>
  <q-layout view="hHh lpR fff">
    <MainHeader />
    <q-page-container>
      <LeftDrawer :menu="allDrawerMenu" />
      <AppBreadcrumb :items="breadcrumbItems" />
      <router-view />
    </q-page-container>
    <MainFooter />
  </q-layout>
</template>

<style scoped></style>
