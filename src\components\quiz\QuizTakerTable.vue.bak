<template>
  <div>
    <div
      v-if="dashboardStore.errorUserAttempts"
      class="text-negative q-mb-md q-pa-sm bg-red-1 rounded-borders"
    >
      <q-icon name="warning" class="q-mr-sm" />
      {{ dashboardStore.errorUserAttempts }}
      <q-btn flat dense label="Retry" @click="retryFetch" class="q-ml-sm" text-color="black" />
    </div>

    <q-table
      :rows="[]"
      :columns="columns"
      row-key="attemptId"
      :loading="dashboardStore.isLoadingUserAttempts"
      v-model:pagination="paginationState"
      @request="handleTableRequest"
      :rows-per-page-options="[5, 10, 15, 25, 50]"
      binary-state-sort
      flat
      bordered
      separator="cell"
      class="q-mt-md text-black"
    >
      <!-- Slot สำหรับแสดง ID (ถ้าต้องการ) -->
      <!-- <template v-slot:body-cell-id="{ row }">
        <q-td class="text-center">{{ row.attemptId }}</q-td>
      </template> -->

      <template v-slot:body-cell-userName="{ row }">
        <q-td class="text-left">{{ row.userName }}</q-td>
      </template>

      <template v-slot:body-cell-submissionDate="{ row }">
        <q-td class="text-center">{{ formatDateTime(row.submissionDate) }}</q-td>
      </template>

      <template v-slot:body-cell-score="{ row }">
        <q-td
          :class="[
            'text-center',
            row.score < (dashboardStore.quizSummary?.highestScore ?? 100) / 2
              ? 'text-negative'
              : 'text-positive',
          ]"
        >
          {{ row.score }}
        </q-td>
      </template>

      <template v-slot:body-cell-actions="">
        <q-td class="text-center">
          <q-btn dense unelevated color="black" icon="article" aria-label="View Answers">
            <q-tooltip>ดูคำตอบ</q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data="{ icon, message, filter }">
        <div class="full-width row flex-center text-accent q-gutter-sm q-pa-md">
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
          <span>
            {{ dashboardStore.currentQuizId === null ? 'Please select a quiz first.' : message }}
          </span>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { ref, watch } from 'vue';
import { useQuizDashboardStore } from 'src/stores/quiz/quizDashboardStore';
// import type {
//   UserAttemptDetailDto,
//   UserAttemptsQueryParams,
// } from 'src/types/quiz/quizDashBoardTypes';
import type { QTableProps } from 'quasar';
import type { DataParams } from 'src/types/data';

const props = defineProps({
  quizId: {
    type: Number as PropType<number | null>,
    required: true, // หรือ false ถ้า component นี้อาจจะถูก render โดยไม่มี quizId ตอนแรก
  },
});

const dashboardStore = useQuizDashboardStore();

// State สำหรับ q-table pagination and sorting
const paginationState = ref<QTableProps['pagination']>({
  sortBy: 'submissionDate', // default sort
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0, // จะถูกอัปเดตจาก store.userAttempts.total
});

// Computed property to map store data to what q-table expects
// const tableRows = computed<UserAttemptDetailDto[]>(() => {
//   return dashboardStore.userAttempts?.data || [];
// });

// Columns definition for q-table
const columns: QTableProps['columns'] = [
  // { name: 'id', label: 'Attempt ID', align: 'center', field: 'attemptId', sortable: true },
  {
    name: 'userName',
    required: true,
    label: 'ชื่อผู้ทำแบบทดสอบ',
    align: 'left',
    field: 'userName',
    sortable: true, // Assuming backend supports sorting by userName
  },
  {
    name: 'submissionDate',
    required: true,
    label: 'วันที่ส่ง',
    align: 'center',
    field: 'submissionDate',
    sortable: true,
  },
  {
    name: 'score',
    required: true,
    label: 'คะแนน',
    align: 'center',
    field: 'score',
    sortable: true,
  },
  {
    name: 'actions',
    label: 'ดูคำตอบ',
    align: 'center',
    field: () => '', // No specific field, just for actions button
  },
];

// Function to handle q-table's @request event for server-side pagination/sorting
async function handleTableRequest(requestProps: {
  pagination: QTableProps['pagination'];
  filter?: string; // If you add filtering
}) {
  const { page, rowsPerPage, sortBy, descending } = requestProps.pagination!;

  // Update local pagination state (q-table will use this)
  if (paginationState.value) {
    paginationState.value.page = page ?? 1;
    paginationState.value.rowsPerPage = rowsPerPage ?? 10;
    paginationState.value.sortBy = sortBy ?? null;
    paginationState.value.descending = descending ?? true;
  }

  if (props.quizId === null) {
    // dashboardStore.userAttempts = null; // Reset store data if needed
    return;
  }

  const params: Partial<DataParams> = {};
  if (typeof page === 'number') params.page = page;
  if (typeof rowsPerPage === 'number') params.limit = rowsPerPage;
  // search: requestProps.filter, // If you implement a search input
  // sortBy: sortBy, // If backend supports sorting by this field name
  // order: descending ? 'DESC' : 'ASC', // If backend uses this format for ordering

  // Backend อาจจะไม่รองรับ sortBy และ order โดยตรงผ่าน UserAttemptsQueryParams
  // ถ้า backend ไม่รองรับการ sort ผ่าน API, q-table จะ sort ข้อมูลที่ดึงมาในหน้าปัจจุบันเท่านั้น

  await dashboardStore.fetchUserAttempts(params, props.quizId);
}

async function retryFetch() {
  if (props.quizId !== null) {
    await handleTableRequest({ pagination: paginationState.value });
  }
}

// Function to view individual user's answers (placeholder)
// function viewUserAnswer() {
// Implement navigation or dialog to show detailed answers
// e.g., router.push({ name: 'UserAttemptDetails', params: { attemptId: attempt.attemptId } });
// }

// Helper to format date/time
function formatDateTime(dateString: string): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    return dateString; // Fallback if date is invalid
  }
}

// Watch for changes in the quizId prop to re-fetch data
watch(
  () => props.quizId,
  async (newQuizId, oldQuizId) => {
    if (newQuizId !== oldQuizId && paginationState.value) {
      // Reset pagination to first page when quizId changes
      paginationState.value.page = 1;
      if (newQuizId !== null) {
        await handleTableRequest({ pagination: paginationState.value });
      } else {
        // Clear data if quizId becomes null
        dashboardStore.userAttempts = null; // This will also clear tableRows via computed
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { immediate: true },
); // Use immediate: true if you want it to run on initial mount if quizId is already set

// Watch for changes in store's userAttempts (e.g., after fetchUserAttempts) to update total rows
watch(
  () => dashboardStore.userAttempts,
  (newAttempts) => {
    if (paginationState.value) {
      if (newAttempts) {
        paginationState.value.rowsNumber = newAttempts.total;
        // q-table v-model:pagination should handle page and rowsPerPage updates,
        // but ensure rowsNumber is accurate for q-table to calculate total pages.
      } else {
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { deep: true },
);

// onMounted(() => {
//   // If props.quizId is set and it's different from what's in store,
//   // the watcher will trigger. If it's the same, and data exists,
//   // it will be used.
//   // If no quizId is passed, table will show "no data" or custom message.
//   if (props.quizId === null && dashboardStore.userAttempts) {
//       // If component is mounted without a quizId but store has old data, clear it.
//       dashboardStore.userAttempts = null;
//   } else if (props.quizId !== null && (!dashboardStore.userAttempts || dashboardStore.currentQuizId !== props.quizId)) {
//     // This case should be handled by the immediate watcher on props.quizId
//     // handleTableRequest({ pagination: paginationState.value });
//   } else if (dashboardStore.userAttempts && dashboardStore.currentQuizId === props.quizId && paginationState.value) {
//     // Data for current quiz is already in store, update paginationState if needed
//     paginationState.value.page = dashboardStore.userAttempts.page;
//     paginationState.value.rowsPerPage = dashboardStore.userAttempts.limit;
//     paginationState.value.rowsNumber = dashboardStore.userAttempts.total;
//     // tableRows will update automatically via computed property
//   }
// });
</script>

<style scoped>
/* Add any component-specific styles here */
</style>

<style scoped>
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: black !important;
}

.edit-graph-icon {
  background-color: #8a64b2;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
