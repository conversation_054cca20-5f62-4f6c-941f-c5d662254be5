import umsFacPersonRoutes from './umsFacPersonRoutes';
import umsFacRoutes from './umsFacRoutes';
import umsPermRoutes from './umsPermRoutes';

const routes = [
  {
    path: '/ums/ums-test1',
    name: 'ums_test1',
    components: {
      default: () => import('src/pages/ums/ums_test1/UmsTest1View.vue'),
      menu: () => import('src/components/ums/MainMenu.vue'),
    },
    meta: {
      layout: 'MainLayout',
      ums: true,
      perms: [1, 2],
    },
  },
  ...umsPermRoutes,
  ...umsFacRoutes,
  ...umsFacPersonRoutes,
];

export default routes;
