<template>
  <q-btn-dropdown
    color="white"
    text-color="black"
    unelevated
    class="rounded-borders"
    style="width: 200px; border: solid 0.5px gray"
    icon="bar_chart"
    dropdown-icon="arrow_drop_down"
    v-model:label="selectedGraph"
  >
    <q-list>
      <q-item clickable v-close-popup @click="selectGraph('กราฟวงกลม')">
        <q-item-section>กราฟวงกลม</q-item-section>
      </q-item>
      <q-item clickable v-close-popup @click="selectGraph('กราฟแท่ง')">
        <q-item-section>กราฟแท่ง</q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';

const props = defineProps<{ modelValue: string }>();
const emit = defineEmits(['update:modelValue']);

const selectedGraph = ref(props.modelValue);

// sync ค่าจาก parent
watch(
  () => props.modelValue,
  (val) => {
    selectedGraph.value = val;
  },
);

// อัปเดตค่ากลับไปให้ parent
function selectGraph(label: string) {
  selectedGraph.value = label;
  emit('update:modelValue', label);
}
</script>
