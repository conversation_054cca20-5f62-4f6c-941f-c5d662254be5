<template>
  <div @click="model = !model" :class="['custom-switch', { on: model }]">
    <div :class="{ slider: true, on: model }"></div>
  </div>
</template>

<script setup lang="ts">
const model = defineModel<boolean>();
</script>

<style scoped lang="scss">
.custom-switch {
  width: 40px;
  height: 24px;
  background-color: $surface;
  border-radius: 999px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.custom-switch.on {
  background-color: $positive;
}
.slider {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
}
.slider.on {
  left: 18px;
}
</style>
