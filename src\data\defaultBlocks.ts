import type { ItemBlock } from 'src/types/models';

/**
 * Default ItemBlocks configuration: 1 header + 1 radio item block
 * This is used as the starting point for new assessments/forms
 */
export const createDefaultBlocks = (assessmentId: number = 1): ItemBlock[] => [
  // Header block
  {
    id: 1,
    assessmentId,
    sequence: 1,
    section: 1,
    type: 'HEADER',
    isRequired: false,
    headerBody: {
      id: 1,
      itemBlockId: 1,
      title: '',
      description: '',
    },
  },
  // Radio item block
  {
    id: 2,
    assessmentId,
    sequence: 2,
    section: 1,
    type: 'RADIO',
    isRequired: false,
    questions: [
      {
        id: 1,
        itemBlockId: 2,
        questionText: '',
        isHeader: false,
        sequence: 1,
        score: 0,
      },
    ],
    options: [
      { id: 1, itemBlockId: 2, optionText: 'ตัวเลือกที่ 1', value: 0, sequence: 1 },
    ],
  },
];

/**
 * Default blocks with assessment ID 1
 */
export const defaultBlocks: ItemBlock[] = createDefaultBlocks(1);
