import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { ItemBlock } from 'src/types/models';

export class ItemBlockService {
  private path = 'item-block';

  async getAllWhereAsmId(assessmentId: number): Promise<ItemBlock[]> {
    try {
      const response = await api.get<ItemBlock[]>(`${this.path}`, { params: { assessmentId } });
      return response.data;
    } catch {
      throw new Error('Fetch item blocks failed');
    }
  }

  // เพิ่ม ItemBlock ใหม่
  async addItemBlock(params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await api.post<ItemBlock>(`${this.path}`, { params });
      return response.data;
    } catch {
      throw new Error('Add item block failed');
    }
  }

  // อัปเดตฟิลด์ของ ItemBlock
  async updateItemBlockField(itemId: number, params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await api.patch<ItemBlock>(`${this.path}/${itemId}`, { params });
      return response.data;
    } catch {
      throw new Error('Update item block field failed');
    }
  }

  // ลบ ItemBlock
  async removeItemBlock(itemId: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${itemId}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
    } catch {
      throw new Error('Remove item block failed');
    }
  }
}
