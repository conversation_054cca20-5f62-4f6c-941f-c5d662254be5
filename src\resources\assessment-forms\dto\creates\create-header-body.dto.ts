import { IsNotEmpty, <PERSON>Optional, IsString, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateHeaderBodyDto {
  @ApiProperty({
    description: 'The title of the header section',
    type: String,
    maxLength: 255,
    example: 'Section 1: Basic Information',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiProperty({
    description: 'Detailed description or instructions for this section',
    type: String,
    required: false,
    example: 'Please answer the following questions about your background.',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID of the item block this header belongs to',
    type: Number,
    required: false,
  })
  @IsOptional()
  itemBlockId?: number;
}
