<script setup lang="ts"></script>
<template>
  <div class="column items-center sidebar">
    <q-btn flat round icon="add" size="14px" class="text-grey" />
    <q-btn flat round icon="title" size="12px" class="text-grey" />
    <q-btn flat round icon="image" size="12px" class="text-grey" />
    <q-btn flat round icon="tune" size="12px" class="text-grey" />
  </div>
</template>
<style scoped>
.sidebar {
  width: 45px;
  background: white;
  border: 1px solid #ccc;
  border-radius: 16px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.08);
}
.q-btn {
  max-width: 35px;
}
</style>
