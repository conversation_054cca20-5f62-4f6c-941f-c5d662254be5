import { Test, TestingModule } from '@nestjs/testing';
import { UmsPermissionController } from './ums_permission.controller';
import { UmsPermissionService } from './ums_permission.service';

describe('UmsPermissionController', () => {
  let controller: UmsPermissionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UmsPermissionController],
      providers: [UmsPermissionService],
    }).compile();

    controller = module.get<UmsPermissionController>(UmsPermissionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
