<template>
  <q-table
    :rows="filteredRows"
    :columns="columns"
    row-key="id"
    :pagination="pagination"
    :rows-per-page-options="[5, 10, 15]"
    flat
    bordered
    separator="cell"
  >
    <template v-slot:body-cell-status="{ row }">
      <q-td class="text-center">
        <span
          :style="{ color: row.status === 'ยังไม่เปิด' || row.status === false ? 'red' : 'green' }"
        >
          {{ row.status === 'ยังไม่เปิด' || row.status === false ? 'ยังไม่ได้ทำ' : 'ทำแล้ว' }}
        </span>
      </q-td>
    </template>

    <!-- Actions Column -->
    <template v-slot:body-cell-actions="{ row }">
      <q-td class="text-center">
        <div class="flex justify-center">
          <q-btn unelevated class="view-icon" icon="article" @click="viewItem(row)" />
        </div>
      </q-td>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import router from 'src/router';
import type { Assessment } from 'src/types/models';
import { ref, computed } from 'vue';

const formData = ref<Assessment[]>([]);

interface Row {
  id: number;
  title: string;
  creator: string;
  date: string;
  status: string;
  toggle: boolean;
}

const rows = ref<Row[]>([]);

const filter = ref('');
const pagination = ref({ rowsPerPage: 5, page: 1 });

const columns = [
  { name: 'id', label: 'รหัส', align: 'center' as const, field: 'id', sortable: true },
  {
    name: 'title',
    label: 'ชื่อแบบสอบถาม',
    align: 'center' as const,
    field: 'name',
    sortable: true,
  },
  { name: 'status', label: 'สถานะ', align: 'center' as const, field: 'status' },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: () => '' },
];

const filteredRows = computed(() => {
  if (!filter.value) return formData.value;
  const keyword = filter.value.toLowerCase();
  return rows.value.filter(
    (row) =>
      row.title.toLowerCase().includes(keyword) ||
      row.creator.toLowerCase().includes(keyword) ||
      row.status.toLowerCase().includes(keyword),
  );
});

// stub functions
async function viewItem(row: Row) {
  await router.push({ path: '/evaluate/user/do-evaluate', query: { id: row.id } });
}

// ! need to implement for standard user
// async function fetchData(pagination: DataParams, programId: number) {
//   const res = await form.getAll(pagination, programId);
//   formData.value = res.data;
// }

// onMounted(async () => {
//   await fetchData(paginationQuery, 1);
// });
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: white !important;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}

.view-icon {
  background-color: #8a64b2;
  color: white;
  border-radius: 12px;
}
</style>
