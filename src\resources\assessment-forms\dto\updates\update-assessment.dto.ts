import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAssessmentDto } from '../creates/create-assessment.dto';
import { isNumber, IsNumber, IsOptional } from 'class-validator';

export class UpdateAssessmentDto extends PartialType(CreateAssessmentDto) {
  @IsNumber()
  @IsOptional()
  timeout?: number;

  @IsNumber()
  @IsOptional()
  totalScore?: number;

  @IsNumber()
  @IsOptional()
  passRatio?: number;
}
