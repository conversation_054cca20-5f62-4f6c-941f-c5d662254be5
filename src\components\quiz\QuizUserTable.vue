<template>
  <q-table :rows="rows" :columns="columns" row-key="id" flat bordered separator="cell" hide-bottom>
    <template v-slot:body-cell-status="{ row }">
      <q-td class="text-center">
        <span :style="{ color: row.status === 'ยังไม่ได้ทำ' ? 'red' : 'green' }">
          {{ row.status === 'ยังไม่ได้ทำ' ? 'ยังไม่ได้ทำ' : 'ทำแล้ว' }}
        </span>
      </q-td>
    </template>

    <!-- Actions Column -->
    <template v-slot:body-cell-actions="{ row }">
      <q-td class="text-center">
        <div class="flex justify-center">
          <q-btn unelevated class="view-icon" icon="article" @click="viewItem(row)" />
        </div>
      </q-td>
    </template>
    <template v-slot:bottom-row>
      <q-tr>
        <q-td colspan="100%">
          <!-- <div class="row justify-end items-center q-gutter-sm q-mt-md">
            <q-select
              dense
              outlined
              v-model="pagination.limit"
              :options="[5, 10, 15]"
              label="จำนวนต่อหน้า"
              style="width: 150px"
              @update:model-value="(val) => (page = 1)"
            />
            <q-pagination v-model="page" :max="totalPages" input direction-links boundary-links />
          </div> -->
        </q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import router from 'src/router';

interface Row {
  id: number;
  title: string;
  status: string;
}

defineProps<{
  rows: Row[];
}>();

const columns = [
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  { name: 'title', label: 'ชื่อแบบทดสอบ', align: 'left' as const, field: 'title', sortable: true },
  { name: 'status', label: 'สถานะ', align: 'center' as const, field: 'status' },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: () => '' },
];
async function viewItem(row: Row) {
  await router.push({
    name: 'user-quiz',
    params: { id: row.id },
  });
  console.log('ดูข้อมูล', row);
}
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: white !important;
}

.view-icon {
  background-color: #8a64b2;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: #8a64b2;
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
