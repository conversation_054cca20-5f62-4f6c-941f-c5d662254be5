import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuestionsService } from './questions.service';
import { QuestionsController } from './questions.controller';
import { Question } from '../entities/question.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { Response } from '../entities/response.entity';
import { FileUploadModule } from '../utils/file-upload.module';

@Module({
  imports: [TypeOrmModule.forFeature([Question, ItemBlock, Response])
  ,FileUploadModule],
  controllers: [QuestionsController],
  providers: [QuestionsService],
  exports: [QuestionsService],
})
export class QuestionsModule {}
