import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type SystemPerm from 'src/types/ums/systemPerm';

interface GetAllPermsParams {
  sortBy: string | null;
  search: { permName: string };
  page: number;
  itemsPerPage: number;
}

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

const getOneSystemPerm = async (perId: number) => {
  try {
    return await api.get(`ums-permission/${perId}`);
  } catch {
    showError('ไม่สามารถโหลดสิทธิ์ระบบได้');
    throw new Error('Get one system perm failed');
  }
};

const getSystemPermActives = async () => {
  try {
    return await api.get('ums-permission/actives');
  } catch {
    showError('ไม่สามารถโหลดสิทธิ์ระบบที่ใช้งานได้');
    throw new Error('Get active system perms failed');
  }
};

const getAllSystemPerms = async (params: GetAllPermsParams) => {
  try {
    return await api.get('ums-permission/perms', { params });
  } catch {
    showError('ไม่สามารถโหลดรายการสิทธิ์ระบบทั้งหมดได้');
    throw new Error('Get all system perms failed');
  }
};

const addSystemPerm = async (data: SystemPerm) => {
  try {
    return await api.post('ums-permission', data);
  } catch {
    showError('ไม่สามารถเพิ่มสิทธิ์ระบบได้');
    throw new Error('Add system perm failed');
  }
};

const editSystemPerm = async (systemPerm: SystemPerm) => {
  try {
    return await api.patch(`ums-permission/${systemPerm.perId}`, systemPerm);
  } catch {
    showError('ไม่สามารถแก้ไขสิทธิ์ระบบได้');
    throw new Error('Edit system perm failed');
  }
};

const deleteSystemPerm = async (perId: number) => {
  try {
    const res = await api.delete(`ums-permission/${perId}`);
    Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
    return res;
  } catch {
    showError('ไม่สามารถลบสิทธิ์ระบบได้');
    throw new Error('Delete system perm failed');
  }
};

export default {
  getOneSystemPerm,
  getSystemPermActives,
  getAllSystemPerms,
  addSystemPerm,
  editSystemPerm,
  deleteSystemPerm,
};
