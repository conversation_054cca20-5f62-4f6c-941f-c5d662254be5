import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseInterceptors,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';

@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new role with the required fields.',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Admin' },
      },
      required: ['name'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  @Post(':id/permissions')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        permissionIds: {
          type: 'array',
          items: { type: 'number' },
          example: [1, 2, 3],
        },
      },
      required: ['permissionIds'],
    },
  })
  addPermissionsToRole(
    @Param('id', ParseIntPipe) id: number,
    @Body('permissionIds') permissionIds: number[],
  ) {
    return this.rolesService.addPermissionsToRole(id, permissionIds);
  }

  @Get()
  findAll() {
    return this.rolesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.findOne(id);
  }

  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new role with the required fields.',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Admin' },
      },
      required: ['name'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor()) 
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateRoleDto: UpdateRoleDto,
  ) {
    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.remove(id);
  }

  @Delete(':id/permissions')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        permissionIds: {
          type: 'array',
          items: { type: 'number' },
          example: [1, 2],
        },
      },
      required: ['permissionIds'],
    },
  })
  removePermissionsFromRole(
    @Param('id', ParseIntPipe) id: number,
    @Body('permissionIds') permissionIds: number[],
  ) {
    return this.rolesService.removePermissionsFromRole(id, permissionIds);
  }

}
