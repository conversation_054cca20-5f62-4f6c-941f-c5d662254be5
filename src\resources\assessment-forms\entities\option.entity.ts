import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('options')
export class Option {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  itemBlockId: number;

  @Column({ type: 'text' })
  optionText: string;

  @Column({ type: 'text', nullable: true })
  imagePath: string | null;

  @Column({ default: 1 })
  value: number;

  @Column()
  sequence: number;

  @Column({ nullable: true, default: null })
  nextSection: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.options, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'itemBlockId' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.selectedOption)
  responses: Response[];
}
