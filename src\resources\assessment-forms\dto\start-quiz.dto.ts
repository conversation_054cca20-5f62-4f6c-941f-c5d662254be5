import { IsNotEmpty, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class StartQuizDto {
  @ApiProperty({
    description: 'ID of the user starting the assessment',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID of the assessment being started',
    type: String, // Fix type to String for linkUrl
  })
  @IsNotEmpty()
  linkUrl: string;
}
