import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { GraylogService } from './graylog/graylog.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private graylogService: GraylogService) {}
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    console.log(`Request... ${request.method} ${request.url}`);

    const bodyString = JSON.stringify(request.body || {});
    const efLogRequestBody =
      bodyString.length > 50000 ? bodyString.substring(0, 50000) : bodyString;

    const eflog = {
      efLogRequest: `Request... ${request.method} ${request.url}`,
      efLogRequestBody,
      efLogResMessage: response.statusMessage,
    };

    await this.graylogService.info(
      `${eflog.efLogRequest} ${eflog.efLogResMessage}`,
      eflog.efLogRequest,
      eflog.efLogRequestBody,
      true,
      'from_web',
    );

    const now = Date.now();
    return next
      .handle()
      .pipe(
        tap(() =>
          console.log(
            `Response... ${Date.now() - now}ms: ${response.statusCode}`,
          ),
        ),
      );
  }
}
