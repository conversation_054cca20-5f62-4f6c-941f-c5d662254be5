<template>
  <q-card
    flat
    bordered
    @click="emit('clickCard', system)"
    class="cursor-pointer"
    style="width: 250px; height: 250px"
    :to="system.sysUrl"
  >
    <q-card-section class="flex flex-center" style="height: 200px">
      <q-icon :name="system.sysIconSrc" color="secondary" size="120px" />
    </q-card-section>

    <div
      class="text-body1 bg-primary text-black text-center q-px-lg"
      style="height: 50px; display: flex; align-items: center; justify-content: center"
    >
      {{ system.sysNameTh }}
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { System } from 'src/types/app';

defineProps<{
  system: System;
}>();

const emit = defineEmits<{
  (e: 'clickCard', system: System): void;
}>();
</script>

<style scoped></style>
