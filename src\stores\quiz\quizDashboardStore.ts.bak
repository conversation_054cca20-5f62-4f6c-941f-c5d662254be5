/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import quizDashboardService from 'src/services/quiz/quizDashboardService';
import type {
  QuizSummaryDto,
  UserAttemptsQueryParams,
  PaginatedUserAttemptsResponse,
  QuizAnswerSummaryResponseDto,
} from 'src/types/quiz/quizDashBoardTypes.ts';
import { useQuasar } from 'quasar';
export const useQuizDashboardStore = defineStore('quizDashboard', () => {
  const $q = useQuasar();
  const currentQuizId = ref<number | null>(null);
  const quizSummary = ref<QuizSummaryDto | null>(null);
  const userAttempts = ref<PaginatedUserAttemptsResponse | null>(null);
  const answerSummary = ref<QuizAnswerSummaryResponseDto | null>(null);

  const isLoadingSummary = ref(false);
  const isLoadingUserAttempts = ref(false);
  const isLoadingAnswerSummary = ref(false);

  const errorSummary = ref<string | null>(null);
  const errorUserAttempts = ref<string | null>(null);
  const errorAnswerSummary = ref<string | null>(null);

  const hasQuizData = computed(() => !!quizSummary.value);
  const overallLoading = computed(() => isLoadingSummary.value || isLoadingUserAttempts.value || isLoadingAnswerSummary.value);

  function setQuizId(id: number | null) {
    if (currentQuizId.value !== id) {
      currentQuizId.value = id;
      // Reset data when quizId changes
      quizSummary.value = null;
      userAttempts.value = null;
      answerSummary.value = null;
      errorSummary.value = null;
      errorUserAttempts.value = null;
      errorAnswerSummary.value = null;

      // Optionally, fetch all data if id is valid
      if (id !== null) {
        // fetchAllQuizData(); // หรือจะให้ component เรียกแยกก็ได้
      }
    }
  }

  async function fetchQuizSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorSummary.value = 'Quiz ID is not set.';
      $q.notify({ type: 'warning', message: errorSummary.value });
      return;
    }

    isLoadingSummary.value = true;
    errorSummary.value = null;
    try {
      quizSummary.value = await quizDashboardService.getQuizSummary(id);
    } catch (err: any) {
      errorSummary.value = err.response?.data?.message || err.message || 'Failed to load quiz summary.';
      $q.notify({ type: 'negative', message: errorSummary.value || 'Error loading quiz summary' });
      quizSummary.value = null;
    } finally {
      isLoadingSummary.value = false;
    }
  }

  async function fetchUserAttempts(params: UserAttemptsQueryParams = {}, quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorUserAttempts.value = 'Quiz ID is not set for fetching user attempts.';
      $q.notify({ type: 'warning', message: errorUserAttempts.value });
      return;
    }

    isLoadingUserAttempts.value = true;
    errorUserAttempts.value = null;
    try {
      userAttempts.value = await quizDashboardService.getUserAttempts(id, params);
    } catch (err: any) {
      errorUserAttempts.value = err.response?.data?.message || err.message || 'Failed to load user attempts.';
      $q.notify({ type: 'negative', message: errorUserAttempts.value || 'Error loading user attempts' });
      userAttempts.value = null;
    } finally {
      isLoadingUserAttempts.value = false;
    }
  }

  async function fetchAnswerSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorAnswerSummary.value = 'Quiz ID is not set for fetching answer summary.';
      $q.notify({ type: 'warning', message: errorAnswerSummary.value });
      return;
    }

    isLoadingAnswerSummary.value = true;
    errorAnswerSummary.value = null;
    try {
      answerSummary.value = await quizDashboardService.getQuizAnswerSummary(id);
    } catch (err: any) {
      errorAnswerSummary.value = err.response?.data?.message || err.message || 'Failed to load answer summary.';
      $q.notify({ type: 'negative', message: errorAnswerSummary.value || 'Error loading answer summary' });
      answerSummary.value = null;
    } finally {
      isLoadingAnswerSummary.value = false;
    }
  }

  // Action to fetch all data for the current quizId
  async function fetchAllQuizDataForCurrentId() {
    if (currentQuizId.value === null) {
      console.warn('Cannot fetch all quiz data, Quiz ID is not set.');
      return;
    }
    // You can run them in parallel
    await Promise.all([
        fetchQuizSummary(currentQuizId.value),
        fetchUserAttempts({}, currentQuizId.value), // Default params for user attempts
        fetchAnswerSummary(currentQuizId.value)
    ]);
  }

  return {
    // State
    currentQuizId,
    quizSummary,
    userAttempts,
    answerSummary,
    isLoadingSummary,
    isLoadingUserAttempts,
    isLoadingAnswerSummary,
    errorSummary,
    errorUserAttempts,
    errorAnswerSummary,

    // Getters
    hasQuizData,
    overallLoading,

    // Actions
    setQuizId,
    fetchQuizSummary,
    fetchUserAttempts,
    fetchAnswerSummary,
    fetchAllQuizDataForCurrentId,
  };
});
