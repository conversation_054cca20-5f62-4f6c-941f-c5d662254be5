{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": ["source.fixAll.eslint"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "typescript.tsdk": "node_modules/typescript/lib", "files.associations": {"*.page-template": "vue", "*.layout-template": "vue", "*.vue": "vue", "*.css": "css", "css": "css", "plyconfig.json": "jsonc"}}