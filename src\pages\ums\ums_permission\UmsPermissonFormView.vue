<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type SystemPerm from 'src/types/ums/systemPerm';
import { useSystemPermStore } from 'src/stores/ums/systemPerm';
// import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import { useUtilsStore } from 'src/stores/utils';

const router = useRouter();
const route = useRoute();
const confirmDlg = ref();
const perId = route.params.perId ? String(route.params.perId) : undefined;
const systemStore = useSystemPermStore();
const systemPerm = ref<SystemPerm>({
  perNameTh: '',
  perNameEn: '',
  updateUser: '',
  updateProgram: '',
});
const form = ref();
const utilsStore = useUtilsStore();

const back = () => {
  void router.push({ name: 'ums_permission' });
};

const editSystemPerm = async () => {
  const { valid } = await form.value.validate();
  if (valid) {
    await systemStore.editSystemPerm(systemPerm.value);
    await confirmDlg.value.openDialog(
      'แจ้งเตือน',
      'success',
      'บันทึกข้อมูลสำเร็จ',
      ``,
      '',
      'ปิด',
      '',
    );
    back();
  }
};

const itemsPath = [
  { title: 'pre-system', disabled: false, icon: 'chevron_right', pathName: 'home' },
  { title: 'จัดการข้อมูลสิทธิ์ในระบบ', disabled: true, pathName: '', icon: 'chevron_right' },
  {
    title: 'จัดการข้อมูลสิทธิ์',
    disabled: false,
    icon: 'chevron_right',
    pathName: 'ums_permission',
  },
  { title: perId ? 'แก้ไขข้อมูลสิทธิ์' : 'เพิ่มข้อมูลสิทธิ์', disabled: true, pathName: '' },
];

onMounted(async () => {
  if (perId) {
    const decryptPerId = utilsStore.decryptString('editId', perId) || -1;
    systemPerm.value = await systemStore.getOneSystemPerm(+decryptPerId);
  }
});
</script>

<template>
  <div class="q-pa-md fontSarabun">
    <q-breadcrumbs class="q-mb-md">
      <q-breadcrumbs-el
        v-for="(item, i) in itemsPath"
        :key="i"
        :label="item.title"
        :to="{ name: item.pathName }"
        :disabled="item.disabled"
      />
    </q-breadcrumbs>

    <q-card flat bordered class="q-pa-md">
      <div class="text-h6 q-mb-md">{{ perId ? 'แก้ไขข้อมูลสิทธิ์' : 'เพิ่มข้อมูลสิทธิ์' }}</div>

      <q-form ref="form" class="q-gutter-md">
        <q-input
          v-model="systemPerm.perNameTh"
          label="ชื่อสิทธิ์ (ท.)"
          outlined
          dense
          :rules="[(v: string) => utilsStore.thaiRuleValidTxt(v, 'กรุณากรอกชื่อสิทธิ์ (ท.)')]"
          counter
          maxlength="50"
        />
        <q-input
          v-model="systemPerm.perNameEn"
          label="ชื่อสิทธิ์ (อ.)"
          outlined
          dense
          :rules="[(v: string) => utilsStore.engRuleValidTxt(v, 'กรุณากรอกชื่อสิทธิ์ (อ.)')]"
          counter
          maxlength="50"
        />

        <div class="row justify-center q-mt-lg">
          <q-btn label="บันทึก" color="primary" @click="editSystemPerm" class="q-mr-sm" />
          <q-btn label="ยกเลิก" color="secondary" flat @click="back" />
        </div>
      </q-form>
    </q-card>

    <!-- <ConfirmDialog ref="confirmDlg" /> -->
  </div>
</template>

<style scoped></style>
