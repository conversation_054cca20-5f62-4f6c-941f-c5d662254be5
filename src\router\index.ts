import { createRouter, createWebHistory } from 'vue-router';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import umsRoutes from './ums/umsRoutes';
import { useAuthStore } from '../stores/auth';
import quizRoutes from './quiz';
import evaluateRoutes from './evaluate';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      component: () => import('layouts/FullLayout.vue'),
      children: [{ path: '', name: 'login', component: () => import('../pages/LoginPage.vue') }],
    },
    {
      path: '/',
      component: () => import('layouts/HomeLayout.vue'),
      children: [
        {
          path: '', // Empty path means this is the default child route
          redirect: { name: 'home' }, // Redirect to the home page
        },
        {
          path: 'home',
          name: 'home',
          component: () => import('../pages/HomePage.vue'),
        },
      ],
    },
    ...umsRoutes,
    ...quizRoutes,
    ...evaluateRoutes,

    {
      path: '/test',
      component: () => import('layouts/MainLayout.vue'),
      children: [
        {
          path: '',
          name: 'test',
          component: () => import('pages/TestPage.vue'),
        },
      ],
    },
    {
      path: '/:catchAll(.*)*',
      component: () => import('pages/ErrorNotFound.vue'),
    },
  ],
});

// // ✅ เช็คว่าผู้ใช้มีสิทธิ์ใดๆ หรือไม่
// const isHavePerms = (user: User): boolean => {
//   return Array.isArray(user?.psnPermissions) && user.psnPermissions.length > 0;
// };

// ✅ Global Route Guard
router.beforeEach(
  (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore();
    const user = authStore.getCurrentUser();
    localStorage.setItem('acs', 'Y');

    if (to.name === 'login') {
      if (!localStorage.getItem('hasVisited')) {
        localStorage.setItem('hasVisited', 'true');
        window.location.reload();
        return;
      }
    }

    if (to.meta.acs) {
      // หากเข้าผ่านลิงก์พิเศษ เช่น email link
      localStorage.setItem('acs', 'Y');
      next();
    } else if (!authStore.isLoggedIn && to.name !== 'login' && to.name !== 'at-test') {
      localStorage.setItem('acs', 'N');
      next({ name: 'login' });
    } else {
      localStorage.setItem('acs', 'N');
      // Allow access even if user.psnPermissions is empty
      // if (to.name !== 'login' && !isHavePerms(user!)) {
      //   next({ name: 'login' });
      //   authStore.showNotifyDialog('ไม่พบสิทธิ์การใช้งาน');
      // }
      if (to.meta.perms) {
        // Check permissions for any route with perms metadata
        const hasPermission = user?.roles?.[0]?.permissions?.some((perm) =>
          (to.meta.perms as number[]).includes(perm.id),
        );
        if (hasPermission) {
          next();
        } else {
          next({ name: 'home' });
        }
      } else {
        next();
      }
    }
  },
);

export default router;
