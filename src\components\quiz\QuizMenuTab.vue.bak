<template>
  <div class="flex bg-grey-2 sub-body" style="padding: 0">
    <q-tabs narrow-indicator v-model="selectedTab" outside-arrows align="center" class="full-width">
      <q-tab
        class="cus-tab"
        v-for="m in filteredMenuItems"
        :key="m.title"
        :name="m.title"
        :label="m.title"
        @click="navigateTo(m.link)"
      ></q-tab>
    </q-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';

// Props are used to determine menu type, even if not directly referenced
defineProps<{
  type: 'form' | 'quiz' | 'ums' | 'default';
}>();

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const selectedTab = ref();

const isDraft = route.params.id === 'new';

async function navigateTo(link: string) {
  if (isDraft && !link.includes(`/quiz/new`)) return; // ❌ ไม่ให้ไปหน้าอื่น
  await router.push(link);
}

const id = route.params.id === 'new' || !route.params.id ? 'new' : String(route.params.id);

// Define local menu items for the create-evaluate page
const localMenuItems = ref([
  {
    title: 'คำถาม',
    icon: 'help',
    link: `/quiz/${id}`,
    perId: [] as number[],
  },
  {
    title: 'การตอบ',
    icon: 'reply',
    link: `/quiz/management/${id}/responses`,
    perId: [] as number[],
  },
  {
    title: 'ตั้งค่า',
    icon: 'settings',
    link: `/quiz/management/${id}/settings`,
    perId: [] as number[],
  },
]);

// Set the selected tab based on the current route
selectedTab.value =
  localMenuItems.value.find((item) => route.path.includes(item.link))?.title ||
  localMenuItems.value[0]?.title;

// Filter menu items based on user permissions
const filteredMenuItems = computed(() => {
  const user = authStore.getCurrentUser();
  return localMenuItems.value.filter((item) => {
    // If no permissions required, show the item
    if (!item.perId || item.perId.length === 0) return true;

    // Check if user has any of the required permissions
    return user?.psnPermissions.some((perm) => item.perId.includes(perm.perId));
  });
});
</script>

<style scoped lang="scss">
.q-tabs {
  height: 40px;
  background-color: white;
}

.cus-tab {
  color: black;
  border-radius: 0px;
  width: 7%;
  height: 40px;
  &.q-tab--active {
    color: var(--q-accent);
    background-color: white;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
  }
}
</style>
