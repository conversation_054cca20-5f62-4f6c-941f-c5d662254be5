<template>
  <q-card class="q-pa-md q-ma-md evaluate-get">
    <q-markdown class="q-ma-md title">{{ formItems.title }}</q-markdown>
    <q-markdown class="q-ma-md description">{{ formItems.description }}</q-markdown>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  title: string;
  description: string;
}>();

const emit = defineEmits(['update:title', 'update:description']);

const formItems = ref({
  title: props.title,
  description: props.description,
});

watch(
  formItems,
  (newValue) => {
    emit('update:title', newValue.title);
    emit('update:description', newValue.description);
  },
  { deep: true },
);
</script>

<style scoped>
.title {
  font-size: 30px;
}

.description {
  font-size: 18px;
}
</style>
