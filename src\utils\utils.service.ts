import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiService } from 'src/api/api.service';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class UtilsService {
  constructor(
    private configService: ConfigService,
    private apiService: ApiService,
  ) {}

  async sendEmailTo(
    system: string,
    to: string,
    subject: string,
    who: string,
    description: string,
    cc: string,
    haveApologize: boolean,
    colorCodeHeader: string,
    bodyTable: { title: string; desp: string }[],
  ) {
    const message = await this.createTemplateTable(
      who,
      description,
      haveApologize,
      colorCodeHeader,
      bodyTable,
    );
    // const emailMessage = `ระบบ: "${system}" หัวเรื่อง: "${subject}" เนื้อความ: ${message}`;
    await this.apiService.sendEmailCC(
      system,
      to == '' ? cc.split(',')[0] : to,
      cc == '' ? to : cc,
      subject,
      message,
    );
  }

  async createTemplateTable(
    who: string,
    description: string,
    haveApologize: boolean,
    colorCodeHeader: string,
    bodyTable: { title: string; desp: string }[],
  ) {
    const first = `<div>เรียน ${who}</div><br>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${description}</div><br>`;

    const last = `<br><div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;จึงเรียนมาเพื่อทราบ ${haveApologize ? 'และขออภัยในความไม่สะดวก' : ''}</div>`;

    // Generate table rows dynamically from bodyTable array
    const tableRows = bodyTable
      .map(
        (row) => `
      <tr>
        <td style="border: 1px solid #dddddd; text-align: left; padding: 8px">${row.title}</td>
        <td style="border: 1px solid #dddddd; text-align: left; padding: 8px">${row.desp}</td>
      </tr>`,
      )
      .join(''); // Join all rows together into a single string

    return `${first}
    <table style="border-collapse: collapse">
      <tr>
        <th
          style="
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            color: white;
            background-color:${colorCodeHeader};
          "
        >
          รายการ
        </th>
        <th
          style="
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            color: white;
            background-color: ${colorCodeHeader};
          "
        >
          รายละเอียด
        </th>
      </tr>
      ${tableRows}
    </table>
    ${last}`;
  }

  encryptObject(secretKey: string, object: any) {
    try {
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(object),
        secretKey,
      ).toString();
      return encrypted;
    } catch (error) {
      console.log(error);
    }
  }

  encryptString(secretKey: string, string: string) {
    try {
      let encrypted = CryptoJS.AES.encrypt(string, secretKey).toString();
      while (encrypted.includes('+')) {
        encrypted = CryptoJS.AES.encrypt(string, secretKey).toString();
      }
      return encrypted;
    } catch (error) {
      console.log(error);
    }
  }

  decryptObject(secretKey: string, encryptedObject: string) {
    try {
      const decrypted = CryptoJS.AES.decrypt(
        encryptedObject,
        secretKey,
      ).toString(CryptoJS.enc.Utf8);
      return decrypted ? JSON.parse(decrypted) : {};
    } catch (error) {
      console.log(error);
    }
  }

  decryptString(secretKey: string, encryptedString: string) {
    try {
      const decrypted = CryptoJS.AES.decrypt(
        encryptedString,
        secretKey,
      ).toString(CryptoJS.enc.Utf8);
      return decrypted;
    } catch (error) {
      console.log(error);
    }
  }

  toLocalDate(
    issueDate: Date,
    typeM: 'short' | 'numeric' | 'narrow' | 'long' | '2-digit',
    typeW: 'short' | 'narrow' | 'long',
    useTime: boolean,
  ) {
    if (!issueDate) return;
    const date = new Date(issueDate);

    // Get the date part in the desired format
    const dateString = date.toLocaleDateString('th-TH', {
      year: 'numeric',
      month: typeM,
      day: 'numeric',
      timeZone: 'Asia/Bangkok',
      weekday: typeW,
    });

    // Get the time part in the desired format
    const timeString = date.toLocaleTimeString('th-TH', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Bangkok',
    });

    if (useTime) {
      // Combine the date and time parts and add the "น." suffix
      return `${dateString} ${timeString} น.`;
    } else {
      return `${dateString}`;
    }
  }

  checkEndDate(month: string, year: string) {
    if (
      month == '01' ||
      month == '03' ||
      month == '05' ||
      month == '07' ||
      month == '08' ||
      month == '10' ||
      month == '12'
    ) {
      return '31';
    } else if (
      month == '04' ||
      month == '06' ||
      month == '09' ||
      month == '11'
    ) {
      return '30';
    } else if (month == '02') {
      return this.getDaysInFebruary(+year) + '';
    }
  }

  getDaysInFebruary(year: number): number {
    return this.isLeapYear(year) ? 29 : 28;
  }

  isLeapYear(year: number): boolean {
    // Leap year is divisible by 4
    // but not divisible by 100 unless it is also divisible by 400
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
  }
}
