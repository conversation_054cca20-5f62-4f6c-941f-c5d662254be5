<!-- <template>
  <div @click="activate" v-if="!active" class="q-pa-sm q-field q-field--filled">
    <div class="text-grey-6">
      {{ content || placeholder }}
    </div>
  </div>

  <quill-editor
    v-else
    v-model:content="content"
    content-type="html"
    toolbar="full"
    toolbar-position="bottom"
    class="q-mt-sm"
    @blur="onBlur"
  />
</template> -->

<template>
  <div v-if="!active" @click="activate" class="q-pa-sm q-field q-field--filled">
    <div class="text-grey-6">
      {{ content || placeholder }}
    </div>
  </div>

  <div v-else class="editor-wrapper">
    <quill-editor
      v-model:content="content"
      content-type="html"
      toolbar="full"
      class="full-editor"
      @blur="onBlur"
    />
  </div>
</template>

<style scoped>
.editor-wrapper {
  display: flex;
  flex-direction: column-reverse; /* 🔻 พลิก editor กับ toolbar */
}

.full-editor {
  width: 100%;
}
</style>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const props = defineProps<{
  placeholder: string;
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const content = ref(props.modelValue); // ✅ local copy

watch(
  () => props.modelValue,
  (val) => {
    content.value = val;
  },
);

watch(content, (val) => {
  emit('update:modelValue', val);
});

const active = ref(false);

function activate() {
  active.value = true;
}

function onBlur() {
  if (!content.value) {
    active.value = false;
  }
}
</script>
