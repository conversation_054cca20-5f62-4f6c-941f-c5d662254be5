import { ref } from 'vue';
import { defineStore } from 'pinia';
import apiService from '../services/apiService';

export const useApiStore = defineStore('api', () => {
  const files = ref<File[]>([]);
  const fileName = ref<string>('');
  const fileType = ref<string>('');
  const filePath = ref<string>('/uploaded_files');
  const getFilenameFromPut = ref();
  const filenameFromGP = ref();
  const pathDownLoad = ref();
  const allPrefix = ref<{ PRF_ID: string; PRF_NAMETH: string }[]>([]);

  //Minio
  async function uploadFile() {
    try {
      const response = await apiService.uploadFile(
        filePath.value,
        fileName.value,
        fileType.value,
        files.value,
      );
      console.log(response);
      getFilenameFromPut.value = response.data.result;
      files.value = [];
      fileName.value = '';
      fileType.value = '';
      filePath.value = '';
    } catch (error) {
      console.log(error);
    }
  }

  async function uploadMultipleFiles() {
    if (!files.value || files.value.length === 0) {
      console.log('No files to upload');
      return;
    }

    for (const file of files.value) {
      try {
        // ตั้งค่า fileName และ fileType ตามไฟล์ปัจจุบัน
        fileName.value = file.name;
        fileType.value = file.type;

        console.log(`Uploading file: ${fileName.value}`);

        const response = await apiService.uploadFile(
          filePath.value,
          fileName.value,
          fileType.value,
          [file], // ส่งไฟล์เป็นอาร์เรย์
        );

        console.log(`Uploaded successfully: ${fileName.value}`, response.data);
      } catch (error) {
        console.error(`Error uploading file: ${file.name}`, error);
      }
    }

    // เคลียร์ค่าเมื่อเสร็จสิ้น
    files.value = [];
    fileName.value = '';
    fileType.value = '';
    console.log('All files uploaded successfully');
    alert('อัปโหลดสำเร็จ');
    void getFileList();
  }

  async function getFileList() {
    try {
      const response = await apiService.getFileList(filePath.value);
      console.log(response);
      return response.data?.result;
    } catch (error) {
      console.log(error);
    }
  }

  async function getPublicFile() {
    try {
      const response = await apiService.getPublicFile(fileName.value);
      filenameFromGP.value = response.data.result.file_1?.view;
      pathDownLoad.value = response.data.result.file_1?.download;
      console.log(response);
    } catch (error) {
      console.log(error);
    }
  }

  async function getPublicFileByName(fileName: string) {
    try {
      const response = await apiService.getPublicFile(fileName);
      return response.data.result.file_1.view;
    } catch (error) {
      console.log(error);
    }
  }

  async function getMultiplePublicFiles(arg: { files: [{ fileName: '' }] }) {
    try {
      if (arg && arg.files.length > 0) {
        const response = await apiService.getPublicFiles(arg);
        //console.log(response)
        return response.data.result;
      }
    } catch (error) {
      console.log(error);
    }
  }

  async function deleteFile() {
    try {
      const response = await apiService.deleteFile(fileName.value);
      console.log(response);
    } catch (error) {
      console.log(error);
    }
  }

  async function dsPrefix() {
    try {
      const response = await apiService.dsPrefix();
      // console.log(response)
      allPrefix.value = response.data.result;
    } catch (error) {
      console.log(error);
    }
  }

  return {
    getMultiplePublicFiles,
    uploadFile,
    files,
    getFileList,
    getPublicFile,
    deleteFile,
    filePath,
    fileType,
    fileName,
    getFilenameFromPut,
    filenameFromGP,
    getPublicFileByName,
    pathDownLoad,
    dsPrefix,
    allPrefix,
    uploadMultipleFiles,
  };
});
