<template>
  <div class="bg-transparent q-pa-md" flat>
    <div v-if="isLoading" class="text-center q-my-xl">
      <q-spinner-dots color="primary" size="3em" />
      <div class="text-subtitle1 q-mt-sm">Loading answer summary...</div>
    </div>

    <q-banner v-else-if="error" inline-actions class="text-white bg-red q-mb-md">
      Error loading answer summary: {{ error }}
      <template v-slot:action>
        <q-btn
          flat
          color="white"
          label="Retry"
          @click="fetchDataForThisComponent"
          :loading="isLoading"
        />
      </template>
    </q-banner>

    <div
      v-else-if="answerSummary && answerSummary.questions && answerSummary.questions.length > 0"
      class="q-mx-auto"
      style="max-width: 1280px; width: 100%"
    >
      <div
        v-for="(question, index) in answerSummary.questions"
        :key="question.questionId"
        class="q-mb-xl"
      >
        <!-- คำถาม -->
        <div class="text-subtitle1 q-mb-sm question-title-underline" style="white-space: pre-wrap">
          คำถามที่ {{ index + 1 }} : {{ formatText(question.questionText, 0) }}
        </div>
        <div class="text-caption text-grey-7 q-mb-sm">{{ question.totalResponses }} Responses</div>

        <!-- layout -->
        <div class="row items-start justify-center q-col-gutter-xl">
          <!-- กราฟ -->
          <div
            v-if="question.choicesSummary && question.choicesSummary.length > 0"
            class="chart-column-wrapper"
          >
            <div class="chart-container q-mt-md">
              <template v-if="selectedGraphType[question.questionId] === 'กราฟวงกลม'">
                <Pie
                  :data="generateChartJsData(question.choicesSummary, 'pie') as ChartData<'pie'>"
                  :options="generateChartJsOptionsPie(question.choicesSummary)"
                  :chart-id="`pie-chart-${question.questionId}`"
                  :dataset-id-key="`pie-dataset-${question.questionId}`"
                  :plugins="chartPlugins"
                  :key="`pie-${question.questionId}-${selectedGraphType[question.questionId]}-${JSON.stringify(question.choicesSummary)}-${question.choicesSummary.find((c) => c.isCorrectOption)?.choiceId}`"
                />
              </template>
              <template v-else-if="selectedGraphType[question.questionId] === 'กราฟแท่ง'">
                <Bar
                  :data="generateChartJsData(question.choicesSummary, 'bar') as ChartData<'bar'>"
                  :options="generateChartJsOptionsBar(question.choicesSummary)"
                  :chart-id="`bar-chart-${question.questionId}`"
                  :dataset-id-key="`bar-dataset-${question.questionId}`"
                  :plugins="chartPlugins"
                  :key="`bar-${question.questionId}-${selectedGraphType[question.questionId]}-${JSON.stringify(question.choicesSummary)}-${question.choicesSummary.find((c) => c.isCorrectOption)?.choiceId}`"
                />
              </template>
            </div>
          </div>
          <div
            v-else-if="question.questionType === 'single' || question.questionType === 'multiple'"
            class="text-grey text-center q-pa-md chart-column-wrapper"
            style="display: flex; align-items: center; justify-content: center"
          >
            No choice data for chart.
          </div>

          <!-- ข้อมูลตอบ -->
          <div class="answer-details-column">
            <div
              class="row justify-between items-center q-mb-md"
              v-if="question.choicesSummary && question.choicesSummary.length > 0"
            >
              <!-- สามารถใส่ title หรือเว้นว่างถ้าไม่ต้องการ -->
              <div class="text-subtitle2"></div>
              <DropDownChart
                :model-value="selectedGraphType[question.questionId] || 'กราฟวงกลม'"
                @update:modelValue="(val) => updateGraphType(question.questionId, val)"
              />
            </div>
            <!-- Choice-based answers -->
            <div v-if="question.choicesSummary && question.choicesSummary.length > 0">
              <div
                v-for="(choice, i) in question.choicesSummary"
                :key="choice.choiceId"
                class="q-pa-sm q-mb-sm rounded-borders row justify-between items-center choice-item"
              >
                <div
                  :class="choice.isCorrectOption ? 'text-positive text-weight-medium' : ''"
                  class="choice-item-text"
                >
                  {{ i + 1 }}. {{ formatText(choice.choiceText, 70) }}
                  <q-badge
                    v-if="choice.isCorrectOption"
                    color="positive"
                    outline
                    label="Correct"
                    class="q-ml-sm"
                  />
                </div>
                <div class="choice-item-stats">
                  <div
                    :class="choice.isCorrectOption ? 'text-positive text-weight-bold' : 'text-dark'"
                  >
                    {{ choice.selectionCount }}
                    ({{ choice.selectionPercentage.toFixed(1) }}%)
                  </div>
                  <div class="text-caption text-grey-7">selected</div>
                </div>
              </div>
            </div>

            <!-- Input-based answers -->
            <div v-if="question.inputAnswersSummary && question.inputAnswersSummary.length > 0">
              <div class="text-subtitle2 q-mb-sm">Top Input Answers:</div>
              <q-list bordered separator>
                <q-item v-for="(inputAns, i) in question.inputAnswersSummary.slice(0, 5)" :key="i">
                  <q-item-section>
                    <q-item-label
                      :class="{
                        'text-positive text-weight-medium': inputAns.isCorrectMatch === true,
                        'text-negative':
                          inputAns.isCorrectMatch === false &&
                          inputAns.isCorrectMatch !== undefined,
                      }"
                    >
                      "{{ inputAns.inputText }}"
                      <q-badge
                        v-if="inputAns.isCorrectMatch === true"
                        color="positive"
                        outline
                        label="Correct Match"
                        class="q-ml-sm"
                      />
                      <q-badge
                        v-if="inputAns.isCorrectMatch === false"
                        color="negative"
                        outline
                        label="Incorrect"
                        class="q-ml-sm"
                      />
                    </q-item-label>
                    <q-item-label caption>
                      {{ inputAns.count }} responses ({{ inputAns.percentage.toFixed(1) }}%)
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
              <div
                v-if="question.inputAnswersSummary.length > 5"
                class="text-caption text-grey q-mt-xs"
              >
                ... and {{ question.inputAnswersSummary.length - 5 }} more unique answers.
              </div>
            </div>

            <div v-if="question.totalResponses === 0" class="text-grey q-mt-sm">
              No responses submitted for this question.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        !isLoading &&
        answerSummary &&
        answerSummary.questions &&
        answerSummary.questions.length === 0
      "
      class="text-center text-grey q-my-xl"
    >
      <q-icon name="quiz" size="3em" />
      <div class="text-subtitle1 q-mt-sm">No questions found in this quiz to display summary.</div>
    </div>
    <div v-else-if="!isLoading && !error" class="text-center text-grey q-my-xl">
      <q-icon name="sentiment_dissatisfied" size="3em" />
      <div class="text-subtitle1 q-mt-sm">No answer summary data available.</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, reactive } from 'vue';
import DropDownChart from 'src/components/DropDownChart.vue';

// ** Chart.js Imports **
import { Bar, Pie } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  ArcElement,
  type ChartData,
  type ChartOptions,
  type Plugin,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import type { Context } from 'chartjs-plugin-datalabels';
import { useQuizStore } from 'src/stores/quiz';
import type { ChoiceSummaryDto, QuizAnswerSummaryResponseDto } from 'src/types/quiz';

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  ArcElement,
  ChartDataLabels,
);

const props = defineProps<{
  quizId: number | null;
}>();

const dashboardStore = useQuizStore();
const selectedGraphType = reactive<Record<number, 'กราฟแท่ง' | 'กราฟวงกลม'>>({});

const isLoading = computed(() => dashboardStore.isLoadingAnswerSummary);
const error = computed(() => dashboardStore.errorAnswerSummary);
const answerSummary = computed<QuizAnswerSummaryResponseDto | null>(
  () => dashboardStore.answerSummary,
);

const formatText = (text: string, maxLength = 180): string => {
  if (!text) return '';
  if (maxLength === 0) return text;
  if (text.length > maxLength) {
    return text.substring(0, maxLength) + '...';
  }
  return text;
};

// Define the specific color for the correct answer
const correctChoiceColor = '#3B71A9';

// Define an ordered list of colors for other (incorrect) choices
// These will be used sequentially for choices that are not marked as correct.
const otherChoiceBaseColors = [
  '#D4AC5F', // Muted Tan/Yellow (from your previous theme)
  '#704E35', // Muted Brown (from your previous theme)
  '#607D8B', // Muted Blue Grey
  '#7E9974', // Muted Olive Green
  '#A1887F', // Muted Taupe / Brownish Pink
  '#BCAAA4', // Lighter Muted Brown/Taupe
  '#546E7A', // Darker Muted Blue Grey
  '#FFAB91', // Muted Peach
  '#90A4AE', // Another Muted Blue Grey
  '#C5E1A5', // Light Muted Green
];

const chartPlugins: Plugin[] = [ChartDataLabels];

const generateChartJsData = (
  choices: ChoiceSummaryDto[] | undefined,
  type: 'bar' | 'pie',
): ChartData<'bar' | 'pie'> => {
  if (!choices || choices.length === 0) {
    return {
      labels: ['No Data'],
      datasets: [
        {
          label: 'Selections',
          data: [1] as number[],
          backgroundColor: ['#E0E0E0'],
          borderColor: ['#BDBDBD'],
          borderWidth: 1,
        },
      ],
    } as ChartData<'bar' | 'pie'>;
  }

  const labels = choices.map((c, i) => `${i + 1}. ${formatText(c.choiceText, 15)}`);
  const data = choices.map((c) => c.selectionCount);

  let otherColorIndex = 0; // Keep track of the index for otherChoiceBaseColors
  const backgroundColors = choices.map((choice) => {
    if (choice.isCorrectOption) {
      return correctChoiceColor;
    } else {
      // Assign colors sequentially from otherChoiceBaseColors for incorrect options
      const color = otherChoiceBaseColors[otherColorIndex % otherChoiceBaseColors.length];
      otherColorIndex++;
      return color;
    }
  });

  return {
    labels: labels,
    datasets: [
      {
        label: 'Selections',
        data: data,
        backgroundColor: backgroundColors,
        borderColor: type === 'pie' ? '#FFFFFF' : backgroundColors, // For bar, border can be same as bg
        borderWidth: type === 'pie' ? 2 : 1,
      },
    ],
  };
};

const generateChartJsOptionsBar = (
  choices: ChoiceSummaryDto[] | undefined,
): ChartOptions<'bar'> => {
  return generateChartJsOptionsBase(choices, 'bar') as ChartOptions<'bar'>;
};

const generateChartJsOptionsPie = (
  choices: ChoiceSummaryDto[] | undefined,
): ChartOptions<'pie'> => {
  return generateChartJsOptionsBase(choices, 'pie') as ChartOptions<'pie'>;
};

const generateChartJsOptionsBase = (
  choices: ChoiceSummaryDto[] | undefined,
  type: 'bar' | 'pie',
): ChartOptions<'bar' | 'pie'> => {
  const isBarChart = type === 'bar';
  const totalResponsesForChoices =
    choices?.reduce((sum, choice) => sum + choice.selectionCount, 0) || 0;

  const options: ChartOptions<'bar' | 'pie'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {},
    layout: {
      padding: {
        top: isBarChart ? 25 : 5,
        bottom: 5,
        left: 5,
        right: 5,
      },
    },
    datasets: {
      bar: {
        barPercentage: 0.65,
        categoryPercentage: 0.7,
      },
    },
    plugins: {
      legend: {
        display: !isBarChart,
        position: 'bottom',
        labels: {
          font: { size: 10 },
          boxWidth: 12,
          padding: 10,
          usePointStyle: true,
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (context) {
            const choiceText = choices?.[context.dataIndex]?.choiceText || context.label || '';
            let label = formatText(choiceText, 50);
            const value = context.parsed.y ?? context.parsed;
            label += `: ${value}`;
            if (choices && context.dataIndex < choices.length) {
              const choice = choices[context.dataIndex];
              if (choice) {
                const percentage = choice.selectionPercentage;
                label += ` (${percentage.toFixed(1)}%)`;
              }
            }
            return label;
          },
        },
      },
      datalabels: {
        color: (context: Context) => {
          if (type === 'pie') {
            const bgColors = Array.isArray(context.dataset.backgroundColor)
              ? context.dataset.backgroundColor
              : [context.dataset.backgroundColor];
            const bgColor = bgColors[context.dataIndex % bgColors.length]; // Use modulo for safety
            if (typeof bgColor === 'string') {
              const r = parseInt(bgColor.slice(1, 3), 16);
              const g = parseInt(bgColor.slice(3, 5), 16);
              const b = parseInt(bgColor.slice(5, 7), 16);
              return r * 0.299 + g * 0.587 + b * 0.114 > 170 ? '#424242' : '#FFFFFF';
            }
            return '#FFFFFF';
          }
          return '#333333';
        },
        anchor: isBarChart ? 'end' : 'center',
        align: isBarChart ? 'top' : 'center',
        offset: isBarChart ? -4 : 0,
        font: {
          weight: 'bold',
          size: 10,
        },
        formatter: (value: number) => {
          if (totalResponsesForChoices === 0 || value === 0) return '';
          const percentage = (value / totalResponsesForChoices) * 100;
          if (percentage < 3 && type === 'pie') return '';
          return `${value}\n(${percentage.toFixed(0)}%)`;
        },
        display: (context: Context) => {
          const value = context.dataset.data[context.dataIndex];
          return typeof value === 'number' && value > 0;
        },
      },
    },
  };

  if (isBarChart) {
    const maxValue = choices ? Math.max(0, ...choices.map((c) => c.selectionCount)) : 0;
    let stepSize = 1;
    if (maxValue > 10) {
      stepSize = Math.ceil(maxValue / 5);
    } else if (maxValue > 0) {
      stepSize = Math.max(1, Math.floor(maxValue / 2)) || 1;
    }

    const barScales: ChartOptions<'bar'>['scales'] = {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: stepSize,
          precision: 0,
        },
      },
      x: {
        ticks: {
          font: { size: 9 },
          maxRotation: 30,
          minRotation: 0,
          autoSkipPadding: 10,
          display: true,
        },
      },
    };
    if (options.datasets) {
      options.datasets.bar = {
        ...(options.datasets.bar || {}),
        barPercentage: 0.65,
        categoryPercentage: 0.7,
      };
    } else {
      options.datasets = { bar: { barPercentage: 0.65, categoryPercentage: 0.7 } };
    }
    options.scales = barScales;
  }

  return options;
};

async function fetchDataForThisComponent() {
  if (props.quizId !== null) {
    if (
      dashboardStore.currentQuizId !== props.quizId ||
      !dashboardStore.answerSummary ||
      dashboardStore.answerSummary.quizId !== props.quizId
    ) {
      await dashboardStore.fetchAnswerSummary(props.quizId);
    }
  }
}

function updateGraphType(questionId: number, newType: 'กราฟแท่ง' | 'กราฟวงกลม') {
  selectedGraphType[questionId] = newType;
}

onMounted(async () => {
  await fetchDataForThisComponent();
});

watch(
  () => props.quizId,
  async () => {
    await fetchDataForThisComponent();
  },
);

watch(
  () => answerSummary.value,
  (newSummary) => {
    if (newSummary && newSummary.quizId === props.quizId && newSummary.questions) {
      newSummary.questions.forEach((q) => {
        if (
          q.choicesSummary &&
          q.choicesSummary.length > 0 &&
          selectedGraphType[q.questionId] === undefined
        ) {
          selectedGraphType[q.questionId] = 'กราฟวงกลม'; // Default to pie chart
        }
      });
    } else if (props.quizId === null || (newSummary && newSummary.quizId !== props.quizId)) {
      for (const key in selectedGraphType) {
        delete selectedGraphType[key];
      }
    }
  },
  { immediate: true, deep: true },
);
</script>

<style scoped>
.text-subtitle1 {
  font-size: 1.1rem;
  line-height: 1.5;
}

.chart-column-wrapper {
  max-width: 450px;
  width: 100%;
  min-height: 300px;
  padding: 0;
  margin-top: 40px;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  box-sizing: border-box;
}

.answer-details-column {
  max-width: 700px;
  width: 100%;
  flex-grow: 1;
}

.choice-item {
  border: 1px solid #e0e0e0;
  font-size: 16px;
  min-height: 70px;
  width: 100%;
  box-shadow: 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}
.choice-item-text {
  white-space: pre-wrap;
  word-break: break-word;
  flex: 1;
  margin-right: 8px;
}
.choice-item-stats {
  white-space: nowrap;
  text-align: right;
  flex-shrink: 0;
}
.question-title-underline {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}
</style>
