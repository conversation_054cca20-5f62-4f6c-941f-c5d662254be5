import { Module } from '@nestjs/common';
import { UmsPermissionService } from './ums_permission.service';
import { UmsPermissionController } from './ums_permission.controller';
import { UmsPermission } from './entities/ums_permission.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([UmsPermission])],
  controllers: [UmsPermissionController],
  providers: [UmsPermissionService],
})
export class UmsPermissionModule {}
