import { ref } from 'vue';
import { defineStore } from 'pinia';
import router from '../router';
import CryptoJS from 'crypto-js';
import { useAuthStore } from './auth';
import type { User } from 'src/types/models';
import { useApiStore } from './api';

interface FacPermission {
  facId: string;
  facName: string;
  perId: number;
  perNameTh: string;
}

export const useUtilsStore = defineStore('utils', () => {
  const isLoading = ref(false);
  const apiStore = useApiStore();
  const authStore = useAuthStore();

  const facId = ref<string>();
  const facName = ref<string>();
  const facPerms = ref<FacPermission[]>([]);

  // function managePerms(user: User | null | undefined, pers: number[]) {
  //   if (!user) {
  //     user = authStore.getCurrentUser();
  //   }

  //   const facPerms0 =
  //     user.role?Permissions.filter((perm: FacPermission) => pers.includes(perm.perId)) || [];

  //   // กรอง facId ไม่ให้ซ้ำ
  //   const nameTracker: Record<string, boolean> = {};
  //   facPerms.value = facPerms0.filter((perm) => {
  //     if (!nameTracker[perm.facId]) {
  //       nameTracker[perm.facId] = true;
  //       return true;
  //     }
  //     return false;
  //   });

  //   // ตั้งค่า facId & facName เริ่มต้น ถ้ายังไม่ถูกตั้ง หรือ fac ปัจจุบันไม่มีในรายการใหม่
  //   if (
  //     facId.value === undefined ||
  //     facPerms.value.findIndex((item) => item.facId === facId.value) <= 0
  //   ) {
  //     facId.value = facPerms.value[0]?.facId || user?.psnPermissions[0]?.facId;
  //     facName.value = facPerms.value[0]?.facName || user?.psnPermissions[0]?.facName;
  //   }
  // }

  function managePerms(user: User | null | undefined, pers: number[]) {
    if (!user) {
      user = authStore.getCurrentUser();
    }

    const rolePermissions = user?.roles?.[0]?.permissions ?? [];

    // จำลอง FacPermission จากข้อมูล Permission
    const facPerms0: FacPermission[] = rolePermissions
      .filter((perm) => pers.includes(perm.id))
      .map((perm) => ({
        facId: 'DEFAULT', // ใส่ mock data ไปก่อน
        facName: 'Default Faculty', // ถ้ามีจริงให้เชื่อมกับข้อมูล faculty
        perId: perm.id,
        perNameTh: perm.name,
      }));

    // กรอง facId ไม่ให้ซ้ำ
    const nameTracker: Record<string, boolean> = {};
    facPerms.value = facPerms0.filter((perm) => {
      if (!nameTracker[perm.facId]) {
        nameTracker[perm.facId] = true;
        return true;
      }
      return false;
    });

    if (
      facId.value === undefined ||
      facPerms.value.findIndex((item) => item.facId === facId.value) < 0
    ) {
      facId.value = facPerms.value[0]?.facId;
      facName.value = facPerms.value[0]?.facName;
    }
  }

  async function openView(fileNamePath: string) {
    const isMobileDevice = /Mobi/i.test(navigator.userAgent);

    if (isMobileDevice) {
      apiStore.fileName = fileNamePath;
      await apiStore.getPublicFile();
      window.location.href = apiStore.pathDownLoad;
    } else {
      const gotoPath = router.resolve({
        name: 'helpdesk_view_pdf',
        query: {
          view: encryptString('encpthviewpdf', fileNamePath, false),
        },
      }).fullPath;
      window.open(gotoPath, '_blank');
    }
  }

  function thaiRuleValidTxt(value: string, message: string) {
    const pattern = /^[ก-๛\s]+$/;
    return !!value || pattern.test(value) || message;
  }

  function engRuleValidTxt(value: string, message: string) {
    const pattern = /^[A-Za-z\s]+$/;
    return !!value || pattern.test(value) || message;
  }

  function encryptObject(secretKey: string, object: unknown): string | undefined {
    try {
      return CryptoJS.AES.encrypt(JSON.stringify(object), secretKey).toString();
    } catch (error) {
      console.error(error);
    }
  }

  function encryptString(
    secretKey: string,
    input: string,
    forParamPath: boolean,
  ): string | undefined {
    try {
      let encrypted = CryptoJS.AES.encrypt(input, secretKey).toString();
      if (forParamPath) {
        while (encrypted.includes('+') || encrypted.includes('/')) {
          encrypted = CryptoJS.AES.encrypt(input, secretKey).toString();
        }
      }
      return encrypted;
    } catch (error) {
      console.error(error);
    }
  }

  function decryptObject(secretKey: string, encryptedObject: string): string | undefined {
    try {
      if (!encryptedObject) return undefined; // Prevents error
      let decrypted: string;
      try {
        decrypted = CryptoJS.AES.decrypt(encryptedObject, secretKey).toString(CryptoJS.enc.Utf8);
      } catch (utf8Error) {
        console.error('Failed to decrypt: Malformed UTF-8 data or invalid ciphertext', utf8Error);
        return undefined;
      }
      if (!decrypted) {
        // Could be wrong key, corrupted input, or not encrypted
        console.error('Failed to decrypt: Empty result, possibly wrong key or corrupted input');
        return undefined;
      }
      try {
        return JSON.parse(decrypted);
      } catch (jsonError) {
        console.error('Failed to parse decrypted JSON:', jsonError, decrypted);
        return undefined;
      }
    } catch (error) {
      console.error('Unexpected error in decryptObject:', error);
      return undefined;
    }
  }

  function decryptString(secretKey: string, encryptedString: string): string | undefined {
    try {
      if (!encryptedString) return undefined; // Prevents error
      return CryptoJS.AES.decrypt(encryptedString, secretKey).toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error(error);
    }
  }

  function toLocalDateThai(
    issueDate: Date,
    typeM: 'short' | 'numeric' | 'narrow' | 'long' | '2-digit',
    typeW: 'short' | 'narrow' | 'long',
    useTime: boolean,
  ): string | undefined {
    if (!issueDate) return;

    const date = new Date(issueDate);

    const dateString = date.toLocaleDateString('th-TH', {
      year: 'numeric',
      month: typeM,
      day: 'numeric',
      timeZone: 'Asia/Bangkok',
      weekday: typeW,
    });

    const timeString = date.toLocaleTimeString('th-TH', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Bangkok',
    });

    return useTime ? `${dateString} ${timeString} น.` : dateString;
  }

  function toThaiNumber(text: number | null): string {
    if (text == null) return '';

    const thaiNumbers = ['๐', '๑', '๒', '๓', '๔', '๕', '๖', '๗', '๘', '๙'];
    return text
      .toString()
      .split('')
      .map((digit) => (digit === '.' ? '.' : thaiNumbers[parseInt(digit, 10)] || digit))
      .join('');
  }

  return {
    isLoading,
    toLocalDateThai,
    toThaiNumber,
    encryptObject,
    decryptObject,
    encryptString,
    decryptString,
    managePerms,
    openView,
    facId,
    facName,
    thaiRuleValidTxt,
    engRuleValidTxt,
    facPerms,
  };
});
