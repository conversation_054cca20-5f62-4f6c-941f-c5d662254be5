import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImageBodiesService } from './image-bodies.service';
import { ImageBodiesController } from './image-bodies.controller';
import { ImageBody } from '../entities/image-body.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { FileUploadModule } from '../utils/file-upload.module';
import { ItemBlocksModule } from '../item-blocks/item-blocks.module';
import { EntityManager } from 'typeorm';

@Module({
  imports: [
    TypeOrmModule.forFeature([ImageBody, ItemBlock]),
    FileUploadModule,
    EntityManager,
    forwardRef(() => ItemBlocksModule), // Ensure circular dependency is handled
   ],
  controllers: [ImageBodiesController],
  providers: [ImageBodiesService],
  exports: [ImageBodiesService],
})
export class ImageBodiesModule {}
