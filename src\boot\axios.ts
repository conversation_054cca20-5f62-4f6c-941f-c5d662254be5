import { boot } from 'quasar/wrappers';
import axios, { type AxiosInstance } from 'axios';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
  }
}

const api = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL || '',
});

api.defaults.headers.common['Access-Control-Origin'] = '*';
api.defaults.headers.common['Access-Control-Allow-Credentials'] = true;

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error instanceof Error ? error : new Error(String(error)));
  },
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      if (
        error.response?.data?.message &&
        error.response.data.message.includes('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง')
      ) {
        return Promise.reject(error instanceof Error ? error : new Error(String(error)));
      }
      // Token refresh and logout logic can be added here if needed
    }
    return Promise.reject(error instanceof Error ? error : new Error(String(error)));
  },
);

export default boot(({ app }) => {
  app.config.globalProperties.$axios = api;
});

export { api };
