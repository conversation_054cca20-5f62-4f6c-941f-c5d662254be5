import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
} from 'typeorm';
import { AssessmentType } from '../enums/assessment-type.enum';
import { Program } from '../../programs/entities/program.entity';
import { ItemBlock } from './item-block.entity';
import { Submission } from './submission.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('assessments')
export class Assessment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: AssessmentType,
    default: AssessmentType.EVALUATE,
  })
  type: AssessmentType;

  @CreateDateColumn({ type: 'datetime' })
  createdAt: Date;

  @Column({ type: 'datetime', nullable: true })
  startAt: Date | null;

  @Column({ type: 'datetime', nullable: true })
  endAt: Date | null;

  @Column({
    default: -1,
    comment: 'จำนวนครั้งที่สามารถส่งแบบประเมินได้ (-1 = ไม่จำกัด)',
  })
  submitLimit: number;

  @Column({ nullable: true })
  linkURL: string;

  @Column({ default: false })
  responseEdit: boolean;

  @Column({ default: false })
  status: boolean;

  @Column({ default: 0 })
  totalScore: number;

  @Column({ default: 0, comment: 'ระยะเวลาที่ต้องการส่งแบบ หน่วยเป็น second' })
  timeout: number;

  @Column({ type: 'decimal', precision: 3, scale: 1, default: 0.5 })
  passRatio: number;

  @Column({ default: false })
  isPrototype: boolean;

  @ManyToOne(() => User, (user) => user.assessments)
  @JoinColumn({ name: 'creator' })
  creator: User;

  @Column()
  programId: number;

  @ManyToOne(() => Program, (program) => program.assessments)
  @JoinColumn({ name: 'programId' })
  program: Program;

  @OneToMany(() => ItemBlock, (itemBlock) => itemBlock.assessment, {
    cascade: true,
  })
  itemBlocks: ItemBlock[];

  @OneToMany(() => Submission, (submission) => submission.assessment, {
    cascade: true,
  })
  submissions: Submission[];
}
