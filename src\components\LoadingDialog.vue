<script setup lang="ts">
import utilsConfigs from 'src/configs/utilsConfigs';
import { useUtilsStore } from 'src/stores/utils'

const utils = useUtilsStore()
</script>

<template>
  <q-dialog v-model="utils.isLoading" persistent width="auto">
    <q-card>
      <q-card-text class="text-center fontSarabun">
        <div>กรุณารอสักครู่...</div>
        <div class="my-2">
          <v-progress-circular :size="45" :color="utilsConfigs.colorSystem" indeterminate></v-progress-circular>
        </div>
      </q-card-text>
    </q-card>
  </q-dialog>
</template>
