import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateUmsPermissionDto } from './dto/create-ums_permission.dto';
import { UpdateUmsPermissionDto } from './dto/update-ums_permission.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UmsPermission } from './entities/ums_permission.entity';
import { Like, Repository } from 'typeorm';
import { DataTablePaginate } from 'src/types/DataTablePaginate';

@Injectable()
export class UmsPermissionService {
  constructor(
    @InjectRepository(UmsPermission)
    private umsPermRepository: Repository<UmsPermission>,
  ) {}

  async create(createUmsPermissionDto: CreateUmsPermissionDto) {
    // const { perNameTh, perNameEn } = createUmsPermissionDto;
    // const systemPerm = new UmsPermission();
    // systemPerm.perNameTh = perNameTh;
    // systemPerm.perNameEn = perNameEn;
    // systemPerm.updateProgram = createUmsPermissionDto.updateProgram;
    // systemPerm.updateUser = createUmsPermissionDto.updateUser;
    const systemPerm = this.umsPermRepository.create(createUmsPermissionDto);
    return this.umsPermRepository.save(systemPerm);
  }

  findAll() {
    return this.umsPermRepository.find();
  }

  findAllActive() {
    return this.umsPermRepository.find({ where: { perStatus: 'Y' } });
  }

  findOne(id: number) {
    return this.umsPermRepository.findOne({
      where: {
        perId: id,
      },
    });
  }

  async findPerms(query): Promise<DataTablePaginate> {
    const page = query.page || 1;
    const itemsPerPage = query.itemsPerPage || 10;
    const skip = (page - 1) * itemsPerPage;
    const sortKey = query.sortBy ? query.sortBy[0].key : 'perId';
    const sortOrder = query.sortBy ? query.sortBy[0].order : 'asc';
    const { permName } = query.search;
    const [systemPerms, total] = await this.umsPermRepository.findAndCount({
      where: [
        { perNameTh: Like(`%${permName}%`) },
        { perNameEn: Like(`%${permName}%`) },
      ],
      order: { [sortKey]: sortOrder },
      take: itemsPerPage,
      skip: skip,
    });

    return {
      data: systemPerms,
      total: total,
      currentPage: page,
      itemsPerPage: itemsPerPage,
    };
  }

  async update(id: number, updateUmsPermissionDto: UpdateUmsPermissionDto) {
    const systemPerm = await this.umsPermRepository.findOne({
      where: { perId: id },
    });

    if (!systemPerm) {
      throw new NotFoundException('system permission not found');
    }

    const updatedPerm = {
      ...systemPerm,
      ...updateUmsPermissionDto,
    };

    return await this.umsPermRepository.save(updatedPerm);
  }

  async remove(id: number) {
    const systemPerm = await this.umsPermRepository.findOne({
      where: { perId: id },
    });

    if (!systemPerm) {
      throw new NotFoundException('system permission not found');
    }
    return this.umsPermRepository.softRemove(systemPerm);
  }
}
