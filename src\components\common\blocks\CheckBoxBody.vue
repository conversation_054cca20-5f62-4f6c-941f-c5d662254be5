<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Drag Indicator, Checkbox, Text Field) -->
    <div
      v-for="(choice, index) in store.checkboxOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event, true)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        class="q-mr-sm"
        @mouseover="store.hoverRow(index)"
      />
      <q-checkbox
        v-model="store.checkboxSelectedOptions"
        :val="store.checkboxOptions[index]!.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="column full-width">
        <div class="row items-center q-col-gutter-sm">
          <!-- Option text input -->
          <div class="col-8">
            <q-input
              v-model="store.checkboxOptions[index]!.optionText"
              :placeholder="store.checkboxOptions[index]!.placeholder"
              dense
              @update:model-value="(value) => handleOptionTextChange(index, String(value || ''))"
              @blur="handleOptionTextBlur(index)"
              class="option-text-input"
            />
          </div>
          <!-- Value input -->
          <div class="col-2">
            <q-input
              v-model.number="store.checkboxOptions[index]!.score"
              type="number"
              dense
              class="value-input"
              @input="updateOptionValue(index)"
            />
          </div>
        </div>
      </div>
      <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      <q-btn
        v-if="store.checkboxOptions.length > 1"
        flat
        round
        icon="close"
        @click="handleDeleteOption(index)"
        :disable="store.checkboxOptions.length <= 1"
        class="q-ml-sm"
      />
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Option } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';
import { Notify } from 'quasar';

// Define interface for option update data
interface OptionUpdateData {
  index: number;
  option: Option;
}

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('CheckBoxAns must be used within an ItemBlock component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

// Define emits for updating parent component
const emit = defineEmits<{
  'update:options': [options: Option[]];
  'option:created': [option: Option];
  'option:updated': [optionId: number, data: OptionUpdateData];
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'value',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Handler for option text changes with debounced auto-save
const handleOptionTextChange = (index: number, newValue: string) => {
  console.log('🔍 Checkbox option text change called with:', { index, newValue });

  // Update the store immediately for UI responsiveness
  store.updateOption(index, true); // true for checkbox

  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];

  // Debug logging to understand the state
  console.log('🔍 Checkbox option text change debug:', {
    index,
    newValue,
    actualOption: actualOption
      ? { id: actualOption.id, optionText: actualOption.optionText }
      : null,
    hasAutoSave: !!autoSave,
    storeOption: store.checkboxOptions[index],
  });

  // Only trigger auto-save if the option has an ID (exists in backend)
  if (actualOption && actualOption.id && autoSave) {
    console.log(
      '✅ Triggering auto-save for checkbox option:',
      actualOption.id,
      'with text:',
      newValue,
    );
    // Use the debounced auto-save from parent component
    autoSave.triggerOptionAutoSave(actualOption.id, 'optionText', newValue);
  } else {
    console.log('❌ Checkbox auto-save not triggered. Reasons:', {
      hasActualOption: !!actualOption,
      hasOptionId: actualOption?.id,
      hasAutoSave: !!autoSave,
    });
  }
};

// Handler for option text blur (immediate save for important changes)
const handleOptionTextBlur = async (index: number) => {
  const actualOption = props.itemBlock.options?.[index];

  // If option doesn't have an ID yet, it needs to be created first
  if (actualOption && !actualOption.id) {
    const optionText = store.checkboxOptions[index]?.optionText || '';

    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: store.checkboxOptions[index]?.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Emit option update to parent component
        emit('option:updated', createdOption.id, {
          index,
          option: createdOption,
        });

        console.log('✅ Checkbox option created on blur:', createdOption);
      } catch (error) {
        console.error('❌ Failed to create checkbox option on blur:', error);
      }
    }
  }
};

// Handler for deleting a checkbox option
const handleDeleteOption = async (index: number) => {
  try {
    // Get the actual option ID from the itemBlock prop
    const actualOption = props.itemBlock.options?.[index];

    // If option has an ID, delete it from backend first
    if (actualOption && actualOption.id) {
      console.log('🗑️ Deleting checkbox option from backend:', actualOption.id);
      await optionService.removeOption(actualOption.id);
      console.log('✅ Checkbox option deleted from backend successfully');
    }

    // Remove from local store (true for checkbox)
    store.removeOption(index, true);

    console.log('✅ Checkbox option removed from store at index:', index);
  } catch (error) {
    console.error('❌ Failed to delete checkbox option:', error);

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถลบตัวเลือกได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  }
};

// Handler for adding new checkbox option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }

    console.log('✅ Checkbox option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create checkbox option:', error);

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือกได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" checkbox option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOtherOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }

    console.log('✅ Other checkbox option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create other checkbox option:', error);

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือก "อื่นๆ" ได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};

// Function to update option value (score)
const updateOptionValue = (index: number) => {
  // Ensure the score is a number
  const score = store.checkboxOptions[index]!.score;
  if (typeof score !== 'number') {
    store.checkboxOptions[index]!.score = Number(score) || 0;
  }

  // Make sure we don't override the updateChoice method's functionality
  store.updateOption(index, true);
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}

.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}

.option-text-input {
  width: 100%;
}

.value-input {
  width: 100%;
  max-width: 120px;
}

.full-width {
  width: 100%;
}
</style>
