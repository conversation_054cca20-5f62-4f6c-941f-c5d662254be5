<template>
  <q-page padding>
    <!-- หัวข้อ -->
    <div class="text-h6 q-mb-md">จัดการแบบทดสอบ</div>

    <!-- แถบบน: Search + เพิ่ม -->
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar @search="onSearchUpdate" />
      <q-btn label="สร้าง" color="accent" icon="add" @click="onClickCreate"> </q-btn>
    </div>

    <q-table
      :rows="rows"
      :columns="quizManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="Loading.isActive"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <template v-slot:body-cell-link="{ row }">
        <q-td class="text-center">
          <q-btn flat dense icon="link" :disable="!row.assessmentLink" aria-label="Open link" />
        </q-td>
      </template>

      <!-- Actions Column (เหมือนเดิม) -->
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              @click="onClickPreview(row)"
            />
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn
              dense
              unelevated
              class="edit-graph-icon"
              icon="bar_chart"
              @click="onClickChart(row)"
            />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar, Loading } from 'quasar';
import { quizManagementColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import SearchBar from 'src/components/SearchBar.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();
const router = useRouter();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);

async function onClickCreate() {
  const user = useAuthStore().getCurrentUser();
  const res = await new AssessmentService('quiz').createOne({
    creatorUserId: user?.id || 1,
    programId: 1,
    type: 'QUIZ',
  });
  await router.push({
    name: 'quiz-edit',
    params: { id: res.id },
    hash: '#questions',
  });
}

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService('quiz').fetchAll(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService('quiz').fetchAll(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    const resolvedRoute = router.resolve({
      name: 'quiz-preview',
      params: { id: row.id.toString() },
    });
    await router.push(resolvedRoute);
  } catch (error) {
    console.error('Navigation to preview failed:', error);
  }
}

async function onClickChart(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    await router.push({
      name: 'quiz-edit',
      params: { id: row.id.toString() },
      hash: 'replies',
    });
  } catch (error) {
    console.error('Navigation to graph view failed:', error);
    notify({ type: 'negative', message: 'ไม่สามารถไปยังหน้ากราฟได้' });
  }
}

async function onClickEdit(row: Assessment) {
  globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
  const resolvedRoute = router.resolve({
    name: 'quiz-edit',
    params: { id: row.id.toString() },
    hash: 'questions',
  });
  await router.push(resolvedRoute);
}

async function onClickDelete(row: Assessment) {
  await new AssessmentService('quiz').deleteOne(row.id);
  await fetchDataRow(pagination.value);
}

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
});
</script>
<style scoped lang="scss">
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: black !important;
}

.view-icon {
  background-color: #39303d;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
