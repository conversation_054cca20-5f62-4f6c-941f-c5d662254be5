<template>
  <div>
    <!-- Error Banner -->
    <q-banner
      v-if="dashboardStore.errorUserAttempts"
      inline-actions
      class="text-white bg-red q-mb-md rounded-borders"
    >
      <template v-slot:avatar>
        <q-icon name="error_outline" color="white" />
      </template>
      {{ dashboardStore.errorUserAttempts }}
      <template v-slot:action>
        <q-btn flat color="white" label="Retry" @click="retryFetch" :loading="isLoading" />
      </template>
    </q-banner>

    <!-- QTable -->
    <q-table
      :rows="tableRows"
      :columns="columns"
      row-key="attemptId"
      :loading="isLoading"
      v-model:pagination="paginationState"
      @request="handleTableRequest"
      :rows-per-page-options="[5, 10, 15, 25, 50]"
      binary-state-sort
      flat
      bordered
      separator="cell"
      class="q-mt-md"
      table-header-class="bg-primary text-black text-h6"
    >
      <template v-slot:body-cell-userName="{ row }">
        <q-td class="text-left">{{ row.userName }}</q-td>
      </template>

      <template v-slot:body-cell-submissionDate="{ row }">
        <q-td class="text-center">{{ formatDateTime(row.submissionDate) }}</q-td>
      </template>

      <template v-slot:body-cell-score="{ row }">
        <q-td
          :class="[
            'text-center',
            row.score < (dashboardStore.quizSummary?.highestScore ?? 100) / 2
              ? 'text-negative'
              : 'text-positive',
          ]"
        >
          {{ row.score }}
        </q-td>
      </template>

      <template v-slot:body-cell-actions="{}">
        <q-td class="text-center">
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="article"
            @click="viewUserAnswer()"
            aria-label="View Answers"
          >
            <q-tooltip>ดูคำตอบ</q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data="{ icon, message, filter }">
        <div class="full-width row flex-center text-accent q-gutter-sm q-pa-md">
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
          <span>
            {{
              dashboardStore.currentQuizId === null && !isLoading
                ? 'Please select a quiz first.'
                : message
            }}
          </span>
        </div>
      </template>

      <!-- Optional: Loading slot for more control (q-table's :loading prop is usually enough) -->
      <!--
      <template v-slot:loading>
        <q-inner-loading showing color="primary" />
      </template>
      -->
    </q-table>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { ref, computed, onMounted, watch } from 'vue';
// import type {
//   UserAttemptDetailDto,
//   UserAttemptsQueryParams,
// } from 'src/types/quiz/quizDashBoardTypes';
import type { QTableProps } from 'quasar';
import { useQuizStore } from 'src/stores/quiz';
import { defaultPaginationValue } from 'src/configs/app.config';

const props = defineProps({
  quizId: {
    type: Number as PropType<number | null>,
    required: true,
  },
});

const dashboardStore = useQuizStore();

const paginationState = ref<Required<QTableProps['pagination']>>({
  sortBy: 'submissionDate',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// --- Computed Properties ---
const isLoading = computed(() => {
  // console.log('[QuizTakerList] isLoading computed:', dashboardStore.isLoadingUserAttempts);
  return dashboardStore.isLoadingUserAttempts;
});

const tableRows = computed<[]>(() => {
  // console.log('[QuizTakerList] tableRows computed. Store userAttempts:', JSON.parse(JSON.stringify(dashboardStore.userAttempts)));
  return dashboardStore.userAttempts?.data || [];
});

// --- Columns Definition ---
const columns: QTableProps['columns'] = [
  {
    name: 'attemptId', // 1. รหัส (ใช้ attemptId)
    label: 'รหัส',
    align: 'center',
    field: (row) => row.attemptId, // ดึงค่าจาก attemptId
    sortable: true,
    required: true, // ถ้าต้องการให้คอลัมน์นี้แสดงเสมอ
  },
  {
    name: 'submissionDate', // 2. วันที่
    label: 'วันที่ทำ', // เปลี่ยน label ให้ชัดเจน
    align: 'center',
    field: 'submissionDate',
    sortable: true,
    required: true,
  },
  {
    name: 'userName', // 3. ชื่อผู้ทำแบบทดสอบ
    label: 'ชื่อผู้ทำแบบทดสอบ',
    align: 'left',
    field: 'userName',
    sortable: true,
    required: true,
  },
  {
    name: 'score', // 4. คะแนน
    label: 'คะแนน',
    align: 'center',
    field: 'score',
    sortable: true,
    required: true,
  },
  {
    name: 'actions', // 5. กระดาษคำตอบ (ปุ่ม Actions)
    label: 'กระดาษคำตอบ', // เปลี่ยน label ให้ชัดเจน
    align: 'center',
    field: () => '', // ไม่ได้ผูกกับ data field โดยตรง
  },
];

// --- Methods ---
async function handleTableRequest(requestProps: {
  pagination: QTableProps['pagination'];
  filter?: string; // For future use if you add table-level filtering
}) {
  const newPagination = requestProps.pagination;
  console.log(
    '[QuizTakerList] handleTableRequest triggered. Quiz ID:',
    props.quizId,
    'Request Pagination:',
    JSON.parse(JSON.stringify(newPagination)),
  );

  if (props.quizId === null) {
    console.warn('[QuizTakerList] handleTableRequest: quizId is null, aborting fetch.');
    dashboardStore.userAttempts = null; // Clear data if quizId is null
    if (paginationState.value) paginationState.value.rowsNumber = 0;
    return;
  }

  // Update local pagination state which q-table uses
  if (paginationState.value && newPagination) {
    paginationState.value.page = newPagination.page ?? 1;
    paginationState.value.rowsPerPage = newPagination.rowsPerPage ?? 10;
    paginationState.value.sortBy = newPagination.sortBy ?? null; // Convert undefined to null
    paginationState.value.descending = newPagination.descending ?? false;
  }

  const params = { ...defaultPaginationValue };

  console.log(
    '[QuizTakerList] Calling dashboardStore.fetchUserAttempts with params:',
    JSON.parse(JSON.stringify(params)),
  );
  await dashboardStore.fetchUserAttempts(params, props.quizId);
  console.log('[QuizTakerList] dashboardStore.fetchUserAttempts finished.');
}

async function retryFetch() {
  console.log('[QuizTakerList] retryFetch called.');
  if (props.quizId !== null && paginationState.value) {
    // Force a re-request with current pagination settings
    await handleTableRequest({ pagination: paginationState.value });
  }
}

function viewUserAnswer() {
  // TODO: Implement actual navigation or dialog display
  // Example: router.push({ name: 'UserAttemptDetailRoute', params: { attemptId: attempt.attemptId }});
}

function formatDateTime(dateString: string): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (e) {
    console.error('[QuizTakerList] Error formatting date:', e);
    return dateString; // Fallback
  }
}

// --- Watchers ---
watch(
  () => props.quizId,
  async (newQuizId, oldQuizId) => {
    console.log(
      `[QuizTakerList] props.quizId watcher triggered. New: ${newQuizId}, Old: ${oldQuizId}`,
    );
    if (newQuizId !== oldQuizId) {
      if (paginationState.value) {
        paginationState.value.page = 1; // Reset to first page
      }
      if (newQuizId !== null && paginationState.value) {
        console.log(
          '[QuizTakerList] props.quizId watcher - calling handleTableRequest due to new quizId.',
        );
        await handleTableRequest({ pagination: paginationState.value });
      } else if (newQuizId === null) {
        console.log(
          '[QuizTakerList] props.quizId watcher - quizId is null, clearing user attempts.',
        );
        dashboardStore.userAttempts = null; // Clear data in store
        if (paginationState.value) paginationState.value.rowsNumber = 0; // Reset total rows
      }
    }
  },
  { immediate: true },
); // `immediate: true` handles initial fetch if quizId is present on mount

watch(
  () => dashboardStore.userAttempts,
  (newAttempts) => {
    console.log(
      '[QuizTakerList] dashboardStore.userAttempts watcher triggered. New attempts:',
      newAttempts ? newAttempts.total : 'null',
    );
    if (paginationState.value) {
      if (newAttempts) {
        paginationState.value.rowsNumber = newAttempts.total;
        // Update page and limit from store if they differ, to keep sync
        // This can happen if fetch was triggered outside this component's pagination flow
        paginationState.value.page = newAttempts.page;
        paginationState.value.rowsPerPage = newAttempts.limit;
      } else {
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { deep: true },
);

// --- Lifecycle Hooks ---
onMounted(() => {
  console.log(`[QuizTakerList] Component mounted. Initial props.quizId: ${props.quizId}`);
  // The `immediate: true` watcher for `props.quizId` should handle the initial data load.
  // Additional onMounted logic can go here if needed, e.g., if data might already be
  // perfectly fine in the store and doesn't need a re-fetch triggered by the watcher.
  // For now, relying on the watcher is cleaner.

  // If, on mount, quizId is valid and data for a *different* quiz is in store,
  // the watcher will trigger a fetch for the correct quizId.
  // If quizId is valid and data for the *same* quiz is in store,
  // the watcher (due to immediate:true) might re-trigger a fetch.
  // This is generally acceptable but could be optimized if needed by adding more checks
  // in the watcher or handleTableRequest.
  // For simplicity and robustness, letting the watcher handle it is fine.
});
</script>

<style scoped>
.edit-graph-icon {
  background-color: #673ab7;
  color: white;
  border-radius: 8px;
}

.edit-graph-icon:hover {
  background-color: #5e35b1;
}
:deep(.q-table thead th) {
  font-size: 1.1rem;
  padding-top: 16px;
  padding-bottom: 16px;
}
:deep(.q-table tbody td) {
  font-size: 1.1rem;
  padding-top: 12px;
  padding-bottom: 12px;
  vertical-align: middle;
  line-height: 1.6;
}

:deep(.q-table tbody td[data-label='รหัส']),
:deep(.q-table tbody td[data-label='คะแนน']) {
  text-align: center;
}

.bg-red .q-banner__content {
  font-size: 1rem;
}

.bg-red .q-btn {
  font-weight: 500;
}

:deep(.q-table__no-data span) {
  font-size: 1.1rem;
  color: #555;
}

:deep(.q-table__no-data .q-icon) {
  color: #777;
}
</style>
