import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from './constants';
import { Request } from 'express';
import { UtilsService } from 'src/utils/utils.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private jwtService: JwtService, private utilsService:UtilsService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
     const encryptionKey = 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P'

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
    const payload = await this.jwtService.verifyAsync(token);

      const decryptedUser = this.utilsService.decryptObject(
        encryptionKey,
        payload.sub,
      );

      if (!decryptedUser) throw new UnauthorizedException('Invalid token');

      request['user'] = decryptedUser;
    } catch {
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
