import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  OneToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('header_bodies')
export class HeaderBody {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string | null;

  @Column()
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.headerBody, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'itemBlockId' })
  itemBlock: ItemBlock;
}
