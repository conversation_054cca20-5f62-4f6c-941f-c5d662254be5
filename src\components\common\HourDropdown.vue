<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: number | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | null): void;
}>();

const selectedHour = ref<number | null>(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    selectedHour.value = newValue;
  },
);

// อัปเดตค่ากลับไปยัง parent ทุกครั้งที่ user เปลี่ยนค่า
watch(selectedHour, (val) => {
  emit('update:modelValue', val);
});

const hours = Array.from({ length: 23 }, (_, i) => ({
  label: `${i + 1}`,
  value: i + 1,
}));
</script>

<template>
  <q-select
    v-model="selectedHour"
    :options="hours"
    emit-value
    map-options
    outlined
    dense
    class="custom-select"
    dropdown-icon="arrow_drop_down"
  >
    <template #selected>
      <span class="custom-placeholder" v-if="selectedHour === null">ชั่วโมง</span>
      <span v-else>{{ selectedHour }}</span>
    </template>
  </q-select>
</template>

<style scoped>
.custom-select {
  width: 100px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
}

.custom-select :deep(.q-field__control) {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-radius: 12px;
  border: 1px solid #bdbdbd;
  background-color: #fff;
}

.custom-select :deep(.q-field__native) {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
  margin: 0;
}

.custom-placeholder {
  color: #9e9e9e;
  font-size: 14px;
}
</style>
